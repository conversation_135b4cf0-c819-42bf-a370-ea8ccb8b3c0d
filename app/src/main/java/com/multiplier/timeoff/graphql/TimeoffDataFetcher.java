package com.multiplier.timeoff.graphql;


import com.multiplier.common.transport.auth.MPLAuthorization;
import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.timeoff.DgsConstants;
import com.multiplier.timeoff.adapters.ContractServiceAdapter;
import com.multiplier.timeoff.graphql.dataloader.TimeOffTypeDefinitionAssignedCountDataLoader;
import com.multiplier.timeoff.graphql.dataloader.TimeOffTypeDefinitionAssignedEntityDataLoader;
import com.multiplier.timeoff.graphql.dataloader.TimeOffTypeUsageDataLoader;
import com.multiplier.timeoff.service.DefinitionService;
import com.multiplier.timeoff.service.TimeoffBreakdownService;
import com.multiplier.timeoff.service.TimeoffCalendarService;
import com.multiplier.timeoff.service.TimeoffQuery;
import com.multiplier.timeoff.service.TimeoffService;
import com.multiplier.timeoff.service.TimeoffSummaryBreakdownService;
import com.multiplier.timeoff.service.TimeoffSummaryQuery;
import com.multiplier.timeoff.service.TimeoffTypeService;
import com.multiplier.timeoff.service.builder.TimeoffFilters;
import com.multiplier.timeoff.types.Company;
import com.multiplier.timeoff.types.CompanyTimeOffPolicyFilter;
import com.multiplier.timeoff.types.Compliance;
import com.multiplier.timeoff.types.Contract;
import com.multiplier.timeoff.types.ContractTimeOff;
import com.multiplier.timeoff.types.ContractTimeOffEntitlement;
import com.multiplier.timeoff.types.PageRequest;
import com.multiplier.timeoff.types.TimeOff;
import com.multiplier.timeoff.types.TimeOffBalanceEncashment;
import com.multiplier.timeoff.types.TimeOffBreakdown;
import com.multiplier.timeoff.types.TimeOffBreakdownInput;
import com.multiplier.timeoff.types.TimeOffCalendar;
import com.multiplier.timeoff.types.TimeOffCalendarFilter;
import com.multiplier.timeoff.types.TimeOffContractRequirements;
import com.multiplier.timeoff.types.TimeOffEncashmentInput;
import com.multiplier.timeoff.types.TimeOffFilter;
import com.multiplier.timeoff.types.TimeOffPolicyAssignmentDetailsResult;
import com.multiplier.timeoff.types.TimeOffPolicyEntityInfo;
import com.multiplier.timeoff.types.TimeOffPolicyFilter;
import com.multiplier.timeoff.types.TimeOffSummariesResponse;
import com.multiplier.timeoff.types.TimeOffSummaryBreakdown;
import com.multiplier.timeoff.types.TimeOffSummaryBreakdownInput;
import com.multiplier.timeoff.types.TimeOffSummaryFilter;
import com.multiplier.timeoff.types.TimeOffTypeDefinition;
import com.multiplier.timeoff.types.TimeOffTypeFilter;
import com.multiplier.timeoff.types.TimeOffTypeInfo;
import com.multiplier.timeoff.types.TimeOffsResponse;
import com.netflix.graphql.dgs.DgsComponent;
import com.netflix.graphql.dgs.DgsData;
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment;
import com.netflix.graphql.dgs.DgsQuery;
import com.netflix.graphql.dgs.InputArgument;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.dataloader.DataLoader;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.access.prepost.PreAuthorize;


@Slf4j
@DgsComponent
@AllArgsConstructor
public class TimeoffDataFetcher {

    private final TimeoffQuery timeoffQuery;
    private final TimeoffSummaryQuery timeoffSummaryQuery;
    private final TimeoffService timeoffService;
    private final ContractServiceAdapter contractServiceAdapter;
    private final MPLAuthorization mplAuthorization;
    private final TimeoffTypeService timeoffTypeService;
    private final DefinitionService definitionService;
    private final TimeoffCalendarService timeOffCalendarService;
    private final TimeoffSummaryBreakdownService timeoffSummaryBreakdownService;
    private final TimeoffBreakdownService timeoffBreakdownService;

    @DgsData(parentType = DgsConstants.CONTRACT.TYPE_NAME, field = DgsConstants.CONTRACT.TimeOff)
    public ContractTimeOff getAll(@InputArgument Long id,
                                  @InputArgument LocalDateTime fromDate,
                                  @InputArgument LocalDateTime toDate,
                                  DgsDataFetchingEnvironment dfe) {
        log.info("[TimeoffDataFetcher] get contract timeoff called with id : {}, from date : {}, to date : {}", id, fromDate, toDate);
        val contract = (Contract) dfe.getSource();
        return timeoffService.getContractTimeOff(contract.getId(), id, fromDate, toDate);
    }



    @DgsData(parentType = DgsConstants.COMPANY.TYPE_NAME, field = DgsConstants.COMPANY.TimeOffs)
    public List<TimeOff> getTimeOffsForCompany(@InputArgument Long id,
                                          @InputArgument LocalDateTime fromDate,
                                          @InputArgument LocalDateTime toDate,
                                          DgsDataFetchingEnvironment dfe) {
        return timeoffQuery.getAllForCompany(dfe, new TimeoffFilters()
                .ids((id == null) ? null : Set.of(id))
                .startDateFrom(fromDate == null ? null : fromDate.toLocalDate())
                .toDate(toDate == null ? null : toDate.toLocalDate()));

    }

    @DgsData(parentType = DgsConstants.COMPLIANCE.TYPE_NAME, field = DgsConstants.COMPLIANCE.TimeOffEntitlement)
    public List<ContractTimeOffEntitlement> getTimeOffEntitlementsForCompliance(DgsDataFetchingEnvironment dfe) {
        Compliance compliance = dfe.getSource();
        Long contractId = compliance.getContract().getId();

        ContractOuterClass.Contract contract = contractServiceAdapter.getContractByIdAnyStatus(contractId);

        if (
                (mplAuthorization.allowed("view.company.contract.compliance") && mplAuthorization.isCompanyUserForCompanyId(contract.getCompanyId()))
                        || (mplAuthorization.allowed("view.member.contract.compliance") && mplAuthorization.isMyMemberId(contract.getMemberId()))
                        || mplAuthorization.allowed("view.operations.contract.compliance")
        ) {
            return timeoffService.getTimeOffEntitlementsForContract(contractId);
        }

        throw new AccessDeniedException(String.format("The current user does not have access to companyId %s and or memberId %s", contract.getCompanyId(), contract.getMemberId()));
    }


    @DgsQuery(field = DgsConstants.QUERY.TimeOffs)
    public TimeOffsResponse timeOffs(@InputArgument TimeOffFilter filter, @InputArgument PageRequest pageRequest) {
        log.info("[TimeoffDataFetcher] fetching timeoffs");
        return timeoffQuery.getAllForOperationsWithPagination(
                filter,
                pageRequest
        );
    }

    @DgsQuery(field = DgsConstants.QUERY.TimeOffSummaries)
    public TimeOffSummariesResponse timeOffSummaries(@InputArgument TimeOffSummaryFilter filter, @InputArgument PageRequest pageRequest) {
        log.info("[TimeoffDataFetcher] fetching timeoff summaries");
        return timeoffSummaryQuery.getAllForOperationsWithPagination(
                filter,
                pageRequest
        );
    }

    @DgsData(parentType = DgsConstants.COMPANY.TYPE_NAME, field = DgsConstants.COMPANY.TimeOffTypeInfos)
    public List<TimeOffTypeInfo> companyTimeOffTypes(DgsDataFetchingEnvironment dfe, @InputArgument TimeOffTypeFilter filter) {
        log.info("[companyTimeOffTypes] fetching company time-off types for filter : {}",filter);
        Company company = dfe.getSource();
        return timeoffTypeService.findCompanyTimeOffTypes(company.getId(), filter);
    }

    @DgsData(parentType = DgsConstants.TIMEOFFTYPEINFO.TYPE_NAME, field = DgsConstants.TIMEOFFTYPEINFO.Usage)
    public CompletableFuture<Long> timeOffTypeUsages(DgsDataFetchingEnvironment dfe) {
        DataLoader<Long, Long> dataLoader = dfe.getDataLoader(TimeOffTypeUsageDataLoader.class);
        TimeOffTypeInfo timeOffTypeInfo = dfe.getSource();
        return dataLoader.load(timeOffTypeInfo.getTypeId());

    }

    @DgsData(parentType = DgsConstants.COMPANY.TYPE_NAME, field = DgsConstants.COMPANY.TimeOffPolicies)
    public List<TimeOffTypeDefinition> companyTimeOffPolicies(@InputArgument CompanyTimeOffPolicyFilter filter) {
        log.info("[companyTimeOffPolicies] with filter : {}", filter);
        return definitionService.findCompanyDefinitions(filter);
    }

    @DgsQuery(field = DgsConstants.QUERY.TimeOffPolicyAssignmentDetails)
    public TimeOffPolicyAssignmentDetailsResult timeOffPolicyAssignmentDetails(@InputArgument Long policyId) {
        log.info("[timeOffPolicyAssignmentDetails] with policy id {}", policyId);
        return definitionService.findDefinitionAssignmentDetails(policyId);
    }

    @PreAuthorize("@me.allowed('view.company.time-off-policy')")
    @DgsData(parentType = DgsConstants.TIMEOFFTYPEDEFINITION.TYPE_NAME, field = DgsConstants.TIMEOFFTYPEDEFINITION.AssignedEmployeeCount)
    public CompletableFuture<Integer> timeOffPolicyAssignedEmployeeCount(DgsDataFetchingEnvironment dfe) {
        DataLoader<Long, Integer> dataLoader = dfe.getDataLoader(TimeOffTypeDefinitionAssignedCountDataLoader.class);
        TimeOffTypeDefinition definition = dfe.getSource();
        return dataLoader.load(definition.getId());
    }

    @DgsQuery(field = DgsConstants.QUERY.TimeOffContractRequirements)
    public TimeOffContractRequirements getTimeOffContractRequirements(@InputArgument Long contractId, DgsDataFetchingEnvironment dfe) {
        log.info("[getTimeOffContractRequirements] with contract id {}", contractId);
        return timeoffService.getTimeOffContractRequirements(contractId, dfe);
    }

    @DgsData(parentType = DgsConstants.TIMEOFFTYPEDEFINITION.TYPE_NAME, field = DgsConstants.TIMEOFFTYPEDEFINITION.AssignedEntityInfo)
    public CompletableFuture<TimeOffPolicyEntityInfo> timeOffPolicyAssignedEntity(DgsDataFetchingEnvironment dfe) {
        DataLoader<Long, TimeOffPolicyEntityInfo> dataLoader = dfe.getDataLoader(TimeOffTypeDefinitionAssignedEntityDataLoader.class);
        TimeOffTypeDefinition definition = dfe.getSource();
        return dataLoader.load(definition.getId());
    }

    @DgsQuery(field = DgsConstants.QUERY.TimeOffPolicies)
    public List<TimeOffTypeDefinition> timeOffPolicies(@InputArgument TimeOffPolicyFilter filter) {
        log.info("[timeOffPolicies] with filter : {}", filter);
        return definitionService.getDefinitions(filter);
    }

    @DgsQuery(field = DgsConstants.QUERY.TimeOffBalanceEncashments)
    public List<TimeOffBalanceEncashment> timeoffEncashmentBalance(@InputArgument TimeOffEncashmentInput input, DgsDataFetchingEnvironment dfe) {
        log.info("[timeoffEncashmentBalance] with input : {}", input);
        return timeoffSummaryQuery.getEncashmentBalance(input, dfe);
    }

    @DgsQuery(field = DgsConstants.QUERY.TimeOffCalendar)
    public TimeOffCalendar timeOffCalendar(DgsDataFetchingEnvironment dfe, @InputArgument TimeOffCalendarFilter filter) {
        log.info("[timeOffCalendar] with filter : {}", filter);
        return timeOffCalendarService.getTimeOffCalendar(dfe, filter);
    }

    @DgsQuery(field = DgsConstants.QUERY.TimeOffSummaryBreakdown)
    public List<TimeOffSummaryBreakdown> timeOffSummaryBreakdown(@InputArgument TimeOffSummaryBreakdownInput input, DgsDataFetchingEnvironment dfe) {
        log.info("[timeOffSummaryBreakdown] with input : {}", input);
        return timeoffSummaryBreakdownService.getSummaryBreakdown(dfe, input);
    }

    @DgsQuery(field = DgsConstants.QUERY.TimeOffBreakdown)
    public List<TimeOffBreakdown> timeOffBreakdown(@InputArgument TimeOffBreakdownInput input,
        DgsDataFetchingEnvironment dfe) {
        log.info("[timeOffBreakdown] with input : {}", input);
        List<TimeOffBreakdown> result = timeoffBreakdownService.getTimeoffBreakdown(dfe, input);

        log.info("[timeOffBreakdown] Successfully completed breakdown request for contractId: {}, returned {} " +
                "breakdown entries",
            input.getContractId(), result.size());
        return result;
    }


}
