package com.multiplier.timeoff.service;


import com.multiplier.common.transport.user.CurrentUser;
import com.multiplier.company.schema.holiday.LegalEntityHoliday;
import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.country.schema.Country;
import com.multiplier.country.schema.holiday.HolidayOuterClass;
import com.multiplier.timeoff.adapters.*;
import com.multiplier.timeoff.core.common.db.AuditUser;
import com.multiplier.timeoff.core.common.db.BaseEntity;
import com.multiplier.timeoff.core.common.dto.TimeOffUsageSummaryDTO;
import com.multiplier.timeoff.core.common.exceptionhandler.MPLError;
import com.multiplier.timeoff.core.common.exceptionhandler.MPLErrorType;
import com.multiplier.timeoff.core.security.AuthorizationService;
import com.multiplier.timeoff.featureflag.FeatureFlagService;
import com.multiplier.timeoff.featureflag.FeatureFlags;
import com.multiplier.timeoff.kafka.TimeoffKafkaPublisher;
import com.multiplier.timeoff.processor.TimeoffValidator;
import com.multiplier.timeoff.repository.*;
import com.multiplier.timeoff.repository.model.*;
import com.multiplier.timeoff.schema.GrpcTimeOffApprovalStatusChangeEvent;
import com.multiplier.timeoff.schema.GrpcTimeOffForPayroll;
import com.multiplier.timeoff.service.builder.TimeoffEntitlementSpecificationBuilder;
import com.multiplier.timeoff.service.builder.TimeoffFilters;
import com.multiplier.timeoff.service.builder.TimeoffSummaryFilters;
import com.multiplier.timeoff.service.exception.DataCorruptionException;
import com.multiplier.timeoff.service.exception.EntityNotFoundException;
import com.multiplier.timeoff.service.exception.ValidationException;
import com.multiplier.timeoff.service.mapper.GrpcTimeoffMapper;
import com.multiplier.timeoff.service.mapper.TimeOffTypeDefinitionMapper;
import com.multiplier.timeoff.service.mapper.TimeoffMapper;
import com.multiplier.timeoff.service.mapper.TimeoffTypeMapper;
import com.multiplier.timeoff.service.notifications.TimeoffNotification;
import com.multiplier.timeoff.types.*;
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.multiplier.timeoff.adapters.CompanyServiceAdapterKt.toCompanyUserFilters;
import static com.multiplier.timeoff.core.util.TimeOffUtil.*;
import static com.multiplier.timeoff.types.TimeOffStatus.APPROVAL_IN_PROGRESS;
import static java.text.MessageFormat.format;

@Service
@RequiredArgsConstructor
@Slf4j(topic = "TimeoffService")
public class TimeoffService {
    private ContractServiceAdapter contractServiceAdapter;
    private CompanyServiceAdapter companyServiceAdapter;
    private ApprovalServiceAdapter approvalServiceAdapter;
    private final TimeoffTypeRepository timeoffTypeRepository;
    private final TimeoffEntitlementDBORepository timeoffEntitlementDBORepository;
    private final TimeoffRepository timeoffRepository;
    private final TimeoffSummaryRepository timeoffSummaryRepository;
    private final EntitlementChangeRecordRepository entitlementChangeRecordRepository;
    private final TimeoffSummaryService timeoffSummaryService;
    private final CurrentUser currentUser;
    private final TimeoffMapper mapper;
    private final GrpcTimeoffMapper grpcTimeoffMapper;
    private final TimeoffNotification timeoffNotification;
    private final HolidayServiceAdapter holidayServiceAdapter;
    private final TimeoffValidator timeoffValidator;
    private final TimeoffKafkaPublisher timeoffKafkaPublisher;
    private final FeatureFlagService featureFlagService;
    private final CompanyDefinitionRepository companyDefinitionRepository;
    private final CountryDefinitionRepository countryDefinitionRepository;
    private final TimeOffTypeDefinitionMapper timeOffTypeDefinitionMapper;
    private final TimeoffEntitlementSpecificationBuilder timeoffEntitlementSpecificationBuilder;
    private final DefinitionService definitionService;
    private final TimeoffTypeMapper timeoffTypeMapper;
    private final DefinitionEntityRepository definitionEntityRepository;
    private final TimeoffQuery timeoffQuery;
    private final TimeoffSummaryQuery timeoffSummaryQuery;
    private final AuthorizationService authorizationService;
    private final TimeoffBreakdownService timeoffBreakdownService;
    private final WorkshiftServiceAdapter workshiftServiceAdapter;
    private final TimeoffSummaryBreakdownService timeoffSummaryBreakdownService;

    private static final String MEMBER_EXP = "member";
    private static final String COMPANY_EXP = "company";
    private static final String OPERATIONS_EXP = "operations";
    private static final String CONTRACT_NOT_FOUND_MESSAGE = "Contract not found for timeoff id = {}";

    private static final Set<TimeOffStatus> STATUSES_ALLOWED_TO_REVOKE_BY_MEMBER = EnumSet.of(
            APPROVAL_IN_PROGRESS,
            TimeOffStatus.APPROVED,
            TimeOffStatus.REJECTED
    );

    private static final Set<TimeOffStatus> STATUSES_ALLOWED_TO_REVOKE_BY_COMPANY_USER = EnumSet.of(
            APPROVAL_IN_PROGRESS,
            TimeOffStatus.APPROVED,
            TimeOffStatus.TAKEN,
            TimeOffStatus.REJECTED
    );

    private static final Set<TimeOffStatus> STATUSES_ALLOWED_TO_UPDATE_BY_MEMBER = EnumSet.of(
            TimeOffStatus.DRAFT
    );

    private static final Set<TimeOffStatus> STATUSES_ALLOWED_TO_UPDATE_BY_COMPANY_USER = EnumSet.of(
            TimeOffStatus.DRAFT
    );

    private static final Set<TimeOffStatus> STATUSES_ALLOWED_TO_DELETE_BY_MEMBER = EnumSet.of(
            TimeOffStatus.DRAFT
    );

    private static final Set<TimeOffStatus> STATUSES_ALLOWED_TO_DELETE_BY_COMPANY_USER = EnumSet.of(
            TimeOffStatus.DRAFT
    );

    private static final Set<TimeOffStatus> STATUSES_ALLOWED_TO_CHANGE_DATE = EnumSet.of(
            APPROVAL_IN_PROGRESS,
            TimeOffStatus.APPROVED,
            TimeOffStatus.TAKEN
    );

    private static final EnumSet<TimeOffStatus> STATUSES_ALLOWED_TO_REVOKE_BY_OPS = EnumSet.of(
            APPROVAL_IN_PROGRESS,
            TimeOffStatus.APPROVED,
            TimeOffStatus.REJECTED,
            TimeOffStatus.TAKEN
    );

    @Autowired
    public void setContractServiceAdapter(@Lazy ContractServiceAdapter contractServiceAdapter) {
        this.contractServiceAdapter = contractServiceAdapter;
    }

    @Autowired
    public void setCompanyServiceAdapter(@Lazy CompanyServiceAdapter companyServiceAdapter) {
        this.companyServiceAdapter = companyServiceAdapter;
    }

    @Autowired
    public void setApprovalServiceAdapter(@Lazy ApprovalServiceAdapter approvalServiceAdapter) {
        this.approvalServiceAdapter = approvalServiceAdapter;
    }

    public TimeoffTypeDBO findAnnualLeaveType() {
        return timeoffTypeRepository.findDefault().orElse(null);
    }

    @Transactional
    public TimeOff submitV2(DgsDataFetchingEnvironment dfe, TimeOffCreateInputV2 createInput) {
        log.info("[submitV2] process started, by currentUser id={}", currentUser.getContext().getId());

        val contract = contractServiceAdapter.getBasicContractById(createInput.getContractId());
        val workshift = workshiftServiceAdapter.getWorkshiftByContract(contract);
        authorizationService.authorize(dfe, contract);
        log.info("[submitV2] Authorization success for contractId: {}", createInput.getContractId());

        // get or create timeoff
        val timeOffDBO = getTargetTimeoffDBO(createInput.getTimeOffId());
        // input validation
        timeoffValidator.preValidateTimeoffCreateV2(contract, createInput, workshift, timeOffDBO);

        // data loading
        val timeOffType = timeoffTypeRepository.getReferenceById(createInput.getTypeId());
        val currentAndNextSummary = timeoffSummaryService.getCurrentAndNextSummaryByContractIdAndTypeId(createInput.getContractId(), createInput.getTypeId());
        val currentSummary = currentAndNextSummary.getFirst();
        val nextSummary = currentAndNextSummary.getSecond();

        // populate data from inputs
        populateTimeoffData(timeOffDBO, contract.getContractId(), createInput, timeOffType);

        // set timeoff entries and no of days
        val result = timeoffBreakdownService.generateTimeOffEntries(timeOffDBO, workshift);
        timeOffDBO.timeoffEntries(result.getLeft());
        timeOffDBO.noOfDays(result.getRight());

        // se timeoff usage
        val usageSummary = timeoffSummaryBreakdownService.applyAndUpdateTimeoffSummaries(timeOffDBO, currentSummary, nextSummary);
        setTimeOffUsage(timeOffDBO, usageSummary);

        // post validation for sufficient balance
        timeoffValidator.throwIfBalanceIsInsufficient(usageSummary);
        log.info("[submitV2] Balance validation passed");

        // save results
        saveResults(timeOffDBO, currentSummary, nextSummary);

        TimeOff timeoff = mapper.map(timeOffDBO);
        timeoffKafkaPublisher.publishTimeoffCreateEvent(timeoff);
        log.info("[submitV2] Successfully created timeoff");

        return timeoff;
    }

    private void setTimeOffUsage(TimeoffDBO timeOffDBO, TimeOffUsageSummaryDTO usageSummaryDTO) {
        timeOffDBO.timeoffUsage(getTimeOffUsage(timeOffDBO, usageSummaryDTO));
    }

    private void saveResults(TimeoffDBO timeoffDBO, TimeoffSummaryDBO currentSummary, TimeoffSummaryDBO nextSummary) {
        List<TimeoffSummaryDBO> summariesToSave = new ArrayList<>();
        if (currentSummary != null) {
            summariesToSave.add(currentSummary);
        }
        if (nextSummary != null) {
            summariesToSave.add(nextSummary);
        }
        timeoffRepository.save(timeoffDBO);
        timeoffSummaryRepository.saveAll(summariesToSave);
    }

    private TimeoffUsageDBO getTimeOffUsage(TimeoffDBO timeOffDBO, TimeOffUsageSummaryDTO usageSummaryDTO) {
        return TimeoffUsageDBO.builder()
                .timeoff(timeOffDBO)
                .usedFromAllocatedCount(usageSummaryDTO.getUsedFromAllocated())
                .usedFromCarryForwardCount(usageSummaryDTO.getUsedFromCarryForward())
                .usedFromLapsableCount(usageSummaryDTO.getUsedFromLapsable())
                .usedFromNextCycleCarryForwardCount(usageSummaryDTO.getUsedFromNextCycleCarryForward())
                .usedFromNextCycleAllocatedCount(usageSummaryDTO.getUsedFromNextCycleAllocated())
                .build();
    }

    @Transactional
    public TimeOff submit(DgsDataFetchingEnvironment dfe, Long contractId, TimeOffCreateInput createInput) {
        log.info("START [create] timeoffId={}, by currentUser id={}", createInput.getId(), currentUser.getContext().getId());

        val contract = getContract(contractId);
        authorizationService.authorize(dfe, contract);

        String experience = currentUser.getContext().getExperience();
        TimeoffDBO targetTimeoffDBO = getTargetTimeoffDBO(createInput.getId());

        validateSubmitTimeOff(targetTimeoffDBO, contract, experience, createInput);

        /* fetch the timeoff_type belongs to my company */
        var entity = mapper.mapFrom(createInput, targetTimeoffDBO.contractId(contract.getId()), timeoffTypeRepository
                .findByKeyAndCompanyId(createInput.getType(), contract.getCompanyId()));
        timeoffRepository.saveAndFlush(entity);
        timeoffSummaryService.onTimeOffChange(entity);

        TimeOff timeoff = mapper.map(entity);
        timeoffKafkaPublisher.publishTimeoffCreateEvent(timeoff);

        return timeoff;
    }

    private void populateTimeoffData(TimeoffDBO timeoffDBO, Long contractId, TimeOffCreateInputV2 input, TimeoffTypeDBO timeoffType) {
        timeoffDBO.type(timeoffType);
        timeoffDBO.contractId(contractId);
        timeoffDBO.startDate(input.getStartDate().getDateOnly());
        timeoffDBO.startSession(input.getStartDate().getSession());
        timeoffDBO.endSession(input.getEndDate().getSession());
        timeoffDBO.endDate(input.getEndDate().getDateOnly());
        timeoffDBO.description(input.getDescription());
        timeoffDBO.status(APPROVAL_IN_PROGRESS);
        timeoffDBO.typeId(timeoffType.id());
    }

    private @NotNull ContractOuterClass.Contract getContract(Long contractId) {
        String experience = currentUser.getContext().getExperience();
        if (MEMBER_EXP.equals(experience)) {
            return getCurrentUserContract();
        }
        return contractServiceAdapter.findContractByContractId(contractId);
    }

    private void validateSubmitTimeOff(TimeoffDBO timeoffDBO,
                                       ContractOuterClass.Contract contract,
                                       String experience,
                                       TimeOffCreateInput createInput) {
        validateContractForTimeoffSubmit(contract);

        // No validations needed for new timeoffs. All access base validations are handled from
        //  the authorization service
        if (!isNewTimeoff(timeoffDBO)) {
            // if not a new timeoff, we need to validate for the created by info and status
            validateExistingTimeoffSubmit(timeoffDBO, contract, experience);
        }
        timeoffValidator.throwIfBalanceIsInsufficient(createInput, contract);


    }

    private void validateContractForTimeoffSubmit(ContractOuterClass.Contract contract) {
        if (!contract.getStarted() && !isStartedLegacy(contract)) {
            throw new ValidationException("Cannot submit because contract has not started. Contract id=" + contract.getId());
        }
    }

    private void validateExistingTimeoffSubmit(TimeoffDBO timeoffDBO, ContractOuterClass.Contract contract, String experience) {
        if (timeoffDBO.status() != TimeOffStatus.DRAFT) {
            throw new ValidationException("Can only submit a new or existing DRAFT timeoff, id = " + timeoffDBO.id());
        }
        if (MEMBER_EXP.equals(experience)) {
            validateIfMemberCanSubmitExistingTimeoff(timeoffDBO, contract);
        } else { // company
            validateIfCompanyUserCanSubmitExistingTimeoff(timeoffDBO);
        }
    }

    private void validateIfMemberCanSubmitExistingTimeoff(TimeoffDBO timeoffDBO, ContractOuterClass.Contract contract) {
        if (isCreatedByUndefined(timeoffDBO)) {
            throwIfTimeoffNotBelongsToMe(timeoffDBO, contract);
            return;
        }

        AuditUser createdByInfo = timeoffDBO.createdByInfo();
        // if this is an existing timeoff which has `created_by_info` = null, comparing created_by only
        if (createdByInfo == null) {
            throwIfCreatedByIsNotMe(timeoffDBO);
            return;
        }

        // this timeoff has `created_by_info` != null, comparing both userId and experience
        throwIfCreatedByInfoIsNotMe(createdByInfo, timeoffDBO);
    }

    private void throwIfTimeoffNotBelongsToMe(TimeoffDBO timeoffDBO, ContractOuterClass.Contract contract) {
        if (!Objects.equals(timeoffDBO.contractId(), contract.getId())) {
            throw new ValidationException("Cannot submit the timeoff id = " + timeoffDBO.id() + ", as it belongs to someone else");
        }
    }

    private void throwIfCreatedByInfoIsNotMe(@NotNull  AuditUser createdByInfo, TimeoffDBO timeoffDBO) {
        // this timeoff has `created_by_info` != null, comparing both userId and experience
        if (COMPANY_EXP.equals(createdByInfo.getExperience()) || !Objects.equals(createdByInfo.getUserId(), currentUser.getContext().getId())) {
            throw new ValidationException("Cannot submit the timeoff id = " + timeoffDBO.id() + ", as it was created in Team/Company view OR not created by you");
        }
    }
    private void throwIfCreatedByIsNotMe(TimeoffDBO timeoffDBO) {
        if (!Objects.equals(timeoffDBO.createdBy(), currentUser.getContext().getId())) {
            throw new ValidationException("Cannot submit the timeoff id = " + timeoffDBO.id() + ", as it was not created by you");
        }
    }

    private void validateIfCompanyUserCanSubmitExistingTimeoff(TimeoffDBO timeoffDBO) {
        if (!isCreatedByUndefined(timeoffDBO)) {
            AuditUser createdByInfo = timeoffDBO.createdByInfo();
            // If this is an existing timeoff which has `created_by_info` = null, comparing created_by only
            if (createdByInfo == null) {
                if (!Objects.equals(timeoffDBO.createdBy(), currentUser.getContext().getId())) {
                    throw new ValidationException("Cannot submit the timeoff id = " + timeoffDBO.id() + ", as it was not created by you");
                }
            } else { // This timeoff has `created_by_info` != null, comparing both userId and experience
                if (MEMBER_EXP.equals(createdByInfo.getExperience()) || !Objects.equals(createdByInfo.getUserId(), currentUser.getContext().getId())) {
                    throw new ValidationException("Cannot submit the timeoff id = " + timeoffDBO.id() + ", as it was created in Personal/Member view OR not created by you");
                }
            }
        }
    }

    public ContractTimeOff getContractTimeOff(Long contractId, Long timeoffId, LocalDateTime fromDate, LocalDateTime toDate) {
        var contract = contractServiceAdapter.getContractByIdAnyStatus(contractId);

        if (!canAccessContractTimeOff(contract)) {
            throw new AccessDeniedException(String.format("The current user does not have access to companyId %s and or memberId %s", contract.getCompanyId(), contract.getMemberId()));
        }

        var summaryFromDate = (fromDate != null) ? fromDate.toLocalDate() : LocalDate.now();
        var summaryToDate = (toDate != null) ? toDate.toLocalDate() : summaryFromDate;

        return new ContractTimeOff(
                // ids: for a specific timeoff; contractIds: should be always set to ensure `ids` don't belong to strange contract
                // fromDate/toDate: not seeing any place on FE passing these values
                timeoffQuery.getAll(new TimeoffFilters()
                        .ids((timeoffId == null) ? null : Set.of(timeoffId))
                        .contractIds(Set.of(contract.getId()))
                        .startDateFrom(fromDate == null ? null : fromDate.toLocalDate())
                        .toDate(toDate == null ? null : toDate.toLocalDate())),

                timeoffSummaryQuery.getAll(new TimeoffSummaryFilters()
                        .contractIds(Set.of(contract.getId()))
                        .fromDate(summaryFromDate)
                        .toDate(summaryToDate)));
    }

    /**
     * Only Member and Company exp can
     */
    private boolean canAccessContractTimeOff(ContractOuterClass.Contract contract) {
        String experience = currentUser.getContext().getExperience();
        return (COMPANY_EXP.equals(experience) && isMyCompany(contract.getCompanyId()))
                || (MEMBER_EXP.equals(experience) && isMyContract(contract.getMemberId()));
    }


    /* END - CREATE/SUBMIT */

    /* START - SAVE AS DRAFT */

    @Transactional
    public TimeOff saveAsDraft(TimeOffSaveAsDraftInput input, DgsDataFetchingEnvironment dfe) {
        log.info("START [saveAsDraft] by currentUser id={}", currentUser.getContext().getId());

        val contract = getContract(input.getContractId());

        authorizationService.authorize(dfe, contract);
        validateTimeoffSaveAsDraft(contract);

        /* fetch the timeoff_type belongs to my company */
        val timeoffType = timeoffTypeRepository.findByKeyAndCompanyId(input.getType(), contract.getCompanyId());
        var timeoffDBO = mapper.mapFrom(input, buildNewTimeoff(contract.getId()), timeoffType);

        timeoffRepository.saveAndFlush(timeoffDBO);
        // because it's still DRAFT, no need to update the summary here

        TimeOff timeoff = mapper.map(timeoffDBO);
        timeoffKafkaPublisher.publishTimeoffCreateEvent(timeoff);

        return timeoff;
    }

    private TimeoffDBO buildNewTimeoff(Long contractId) {
        return TimeoffDBO.builder()
                .contractId(contractId)
                .build();
    }

    private void validateTimeoffSaveAsDraft(ContractOuterClass.Contract contract) {
        if (!contract.getStarted() && !isStartedLegacy(contract)) {
            throw new ValidationException("Cannot save as draft because the contract has not started. Contract id=" + contract.getId());
        }
    }

    /* END - SAVE AS DRAFT */

    /* START - REVOKE */
    @Transactional
    public TimeOff revoke(DgsDataFetchingEnvironment dfe, Long id) {
        log.info("START [revoke] timeoffId={}, by currentUser id={}", id, currentUser.getContext().getId());

        val timeoffDBO = getOrThrowTimeoffById(id);
        val contract = getContract(timeoffDBO.contractId());
        authorizationService.authorize(dfe, contract);
        validateRevokeTimeoff(timeoffDBO);

        timeoffDBO.status(TimeOffStatus.DRAFT);
        TimeoffDBO savedTimeoffDBO = timeoffRepository.saveAndFlush(timeoffDBO);
        timeoffSummaryService.onTimeOffChange(savedTimeoffDBO);

        TimeOff timeoff = mapper.map(savedTimeoffDBO);
        timeoffKafkaPublisher.publishTimeoffUpdateEvent(timeoff);


        return timeoff;
    }

    private TimeoffDBO getOrThrowTimeoffById(Long id) {
        return timeoffRepository.findById(id).orElseThrow(() -> new EntityNotFoundException("timeoff does not exist for given id " + id));
    }

    private void validateRevokeTimeoff(TimeoffDBO timeoffDBO) {
        String experience = currentUser.getContext().getExperience();
        // validate status allowed to revoke
        validateStatusForRevoke(timeoffDBO, experience);
        validateTimeoffCreatorByExperience(timeoffDBO, experience);
    }

    private void validateStatusForRevoke(TimeoffDBO timeoffDBO, String experience) {
        if (timeoffDBO.status() == TimeOffStatus.DRAFT) {
            throw new ValidationException("Timeoff id = " + timeoffDBO.id() + " is already in DRAFT status");
        }
        if ((MEMBER_EXP.equals(experience) && !STATUSES_ALLOWED_TO_REVOKE_BY_MEMBER.contains(timeoffDBO.status())) ||
                (COMPANY_EXP.equals(experience) && !STATUSES_ALLOWED_TO_REVOKE_BY_COMPANY_USER.contains(timeoffDBO.status()))){
            throw new ValidationException("The timeoff id = " + timeoffDBO.id() + " is in status = " + timeoffDBO.status() + ", so cannot revoke");
        }
    }

    private void validateTimeoffCreatorByExperience(TimeoffDBO timeoffDBO, String experience) {
        if (MEMBER_EXP.equals(experience)) {
            validateTimeoffCreatorByMemberExperience(timeoffDBO);
        } else if (COMPANY_EXP.equals(experience)) {
            validateTimeoffCreatorByCompanyExperience(timeoffDBO);
        }
    }

    private void validateTimeoffCreatorByMemberExperience(TimeoffDBO timeoffDBO) {
        if (isCreatedByUndefined(timeoffDBO)) {
            // if created by undefined, we don't need to validate if timeoff belongs to the current user
            // since an ABAC library validate it at the start of the flow
            return;
        }
        AuditUser createdByInfo = timeoffDBO.createdByInfo();
        if (createdByInfo == null) {
            if (!Objects.equals(timeoffDBO.createdBy(), currentUser.getContext().getId())) {
                throw new ValidationException("Cannot access the timeoff id = " + timeoffDBO.id() + ", as it was not created by you");
            }
        } else { // this timeoff has `created_by_info` != null, comparing both userId and experience
            if (COMPANY_EXP.equals(createdByInfo.getExperience()) || !Objects.equals(createdByInfo.getUserId(), currentUser.getContext().getId())) {
                throw new ValidationException("Cannot access the timeoff id = " + timeoffDBO.id() + ", as it was created in Team/Company view OR not created by you");
            }
        }
    }

    private void validateTimeoffCreatorByCompanyExperience(TimeoffDBO timeoffDBO) {
        var companyUser = companyServiceAdapter.getCompanyUser(toCompanyUserFilters(currentUser));
        if (companyUser.getIsAdmin() || isPayrollAdmin(companyUser) || isHrAdmin(companyUser)) {
            // HRIS 865d7tfyv - Admin should be able to revoke any contract's timeoffs (within the company)
            return;
        }

        if (isCreatedByUndefined(timeoffDBO)) {
            // Created_by field is missing for validations. So if the current user has access to the contract (already validated by ABAC),
            // we can ignore any validations
            return;
        }

        AuditUser createdByInfo = timeoffDBO.createdByInfo();
        if (createdByInfo == null) {
            if (!Objects.equals(timeoffDBO.createdBy(), currentUser.getContext().getId())) {
                throw new ValidationException("Cannot access the timeoff id = " + timeoffDBO.id() + ", as it was not created by you");
            }
            return;
        }

        if (MEMBER_EXP.equals(createdByInfo.getExperience()) || !Objects.equals(createdByInfo.getUserId(), currentUser.getContext().getId())) {
            throw new ValidationException("Cannot access the timeoff id = " + timeoffDBO.id() + ", as it was created in Personal/Member view OR not created by you");
        }

    }

    /* END - REVOKE */

    /* START - UPDATE */

    @Transactional
    public TimeOff update(Long id, TimeOffUpdateInput value, DgsDataFetchingEnvironment dfe) {
        log.info("START [update] id={} by currentUser id={}", id, currentUser.getContext().getId());

        var timeoffDBO = getOrThrowTimeoffById(id);
        val contract = getContract(timeoffDBO.contractId());

        authorizationService.authorize(dfe, contract);
        validateTimeoffUpdate(timeoffDBO);

        /* fetch the timeoff_type belongs to my company */
        timeoffDBO = mapper.mapFrom(value, timeoffDBO, timeoffTypeRepository.findByKeyAndCompanyId(value.getType(), contract.getCompanyId()));
        timeoffRepository.saveAndFlush(timeoffDBO);
        timeoffSummaryService.onTimeOffChange(timeoffDBO);

        TimeOff timeoff = mapper.map(timeoffDBO);
        timeoffKafkaPublisher.publishTimeoffUpdateEvent(timeoff);

        return timeoff;
    }

    private void validateTimeoffUpdate(TimeoffDBO timeoffDBO) {
        String experience = currentUser.getContext().getExperience();
        validateStatusForUpdate(timeoffDBO, experience);
        validateTimeoffCreatorByExperience(timeoffDBO, experience);
    }

    private void validateStatusForUpdate(TimeoffDBO timeoffDBO, String experience) {
        if (MEMBER_EXP.equals(experience) && !STATUSES_ALLOWED_TO_UPDATE_BY_MEMBER.contains(timeoffDBO.status()) ||
                (COMPANY_EXP.equals(experience) && !STATUSES_ALLOWED_TO_UPDATE_BY_COMPANY_USER.contains(timeoffDBO.status()))) {
            throw new ValidationException("The timeoff id = " + timeoffDBO.id() + " is in status = " + timeoffDBO.status() + ", so cannot update");
        }
    }

    /* END - UPDATE */

    /* START - DELETE */

    @Transactional
    public void delete(Long id, DgsDataFetchingEnvironment dfe) {
        log.info("START [delete] id={} by currentUser id={}", id, currentUser.getContext().getId());

        var timeoffDBO = getOrThrowTimeoffById(id);
        var contract = getContract(timeoffDBO.contractId());

        authorizationService.authorize(dfe, contract);
        validateTimeoffDelete(timeoffDBO);
        timeoffDBO.status(TimeOffStatus.DELETED);
        timeoffKafkaPublisher.publishTimeoffDeleteEvent(mapper.map(timeoffDBO));
        timeoffSummaryService.onTimeOffChange(timeoffDBO);
    }

    private void validateTimeoffDelete(TimeoffDBO timeoffDBO) {
        String experience = currentUser.getContext().getExperience();
        validateStatusForDelete(timeoffDBO, experience);
        validateTimeoffCreatorByExperience(timeoffDBO, experience);
    }

    private void validateStatusForDelete(TimeoffDBO timeoffDBO, String experience) {
        if ((MEMBER_EXP.equals(experience) && !STATUSES_ALLOWED_TO_DELETE_BY_MEMBER.contains(timeoffDBO.status())) ||
                (COMPANY_EXP.equals(experience) && !STATUSES_ALLOWED_TO_DELETE_BY_COMPANY_USER.contains(timeoffDBO.status()))) {
            throw new ValidationException("The timeoff id = " + timeoffDBO.id() + " is in status = " + timeoffDBO.status() + ", so cannot delete");
        }

    }

    /* END - DELETE */


    /**
     * called by contract domain<br>
     * The logic: InDB[A, B] + request[B+C] = InDBNow[B+C]<br>
     * That means missing timeoff types will be deleted (if some conditions are met)<br>
     *
     * @param contractID guaranteed to be valid by contract domain
     * @param inputs     the input from FE
     */
    @Transactional
    public void updateTimeOffEntitlements(Long contractID, List<ContractUpdateTimeOffEntitlementsInput> inputs) {
        ContractOuterClass.Contract contract = contractServiceAdapter.findContractByContractId(contractID);
        throwIfContractCannotUpdateEntitlements(contract);
        log.info("[updateTimeOffEntitlements] Updating entitlements for contract id : {}, inputs : {}", contract.getId(), inputs);

        if (inputs == null) {
            inputs = Collections.emptyList();
        }
        List<TimeoffEntitlementDBO> existingEntitlements = timeoffEntitlementDBORepository.findAllByContractId(contract.getId());
        if (contract.getType() == ContractOuterClass.ContractType.HR_MEMBER) {
            updateEntitlementsForHRMember(contract, inputs, existingEntitlements);
        } else {
            updateEntitlementsForEOR(inputs, existingEntitlements, contract);
        }
    }

    private void updateEntitlementsForHRMember(ContractOuterClass.Contract contract, List<ContractUpdateTimeOffEntitlementsInput> inputs,
                                               List<TimeoffEntitlementDBO> existingEntitlements) {
        Map<Long, TimeOffDefinitionEntity> typeIdToDefinitionMap = getTypeIdToDefinitionMapForHRMember(contract, existingEntitlements);
        Map<Long, TimeoffTypeDBO> idToTimeOffTypeMap = getIdToTimeOffTypeDBOMap(typeIdToDefinitionMap.keySet());

        Map<String, TimeoffTypeDBO> keyToTimeOffTypeMap = getKeyToTimeOffTypeMap(idToTimeOffTypeMap.values());
        Map<String, TimeoffEntitlementDBO> keyToExistingEntitlementsMap = getKeyToEntitlementMap(idToTimeOffTypeMap, existingEntitlements);

        throwIfHrMemberCannotUpdateEntitlements(inputs, keyToExistingEntitlementsMap, contract, keyToTimeOffTypeMap, typeIdToDefinitionMap);
        processEntitlementsUpdateForHRMember(inputs, keyToExistingEntitlementsMap);
    }

    private void updateEntitlementsForEOR(List<ContractUpdateTimeOffEntitlementsInput> inputs,
                                          List<TimeoffEntitlementDBO> existingEntitlements,
                                          ContractOuterClass.Contract contract)  {
        Map<Long, TimeOffDefinitionEntity> defIdToEligibleDefinitionMap = getDefinitionIdToEligibleDefinitionsMap(contract);
        Map<Long, TimeOffDefinitionEntity> defIdToExistingDefinitionMap = getDefinitionIdToExistingDefinitionsMap(contract.getCompanyId(), existingEntitlements, defIdToEligibleDefinitionMap);
        Map<Long, TimeOffDefinitionEntity> defIdToAvailableDefinitionMap = getDeinitionIdToAvailableDefinitionMap(defIdToEligibleDefinitionMap, defIdToExistingDefinitionMap);
        Map<Long, TimeOffDefinitionEntity> typeIdToAvailableDefinitionMap = toTypeIdToDefinitionMap(defIdToAvailableDefinitionMap);

        Map<Long, TimeoffTypeDBO> idToTimeOffTypeMap = getIdToTimeOffTypeDBOMap(getAllTypeIds(defIdToExistingDefinitionMap.values(), defIdToAvailableDefinitionMap.values()));
        Map<String, TimeoffTypeDBO> keyToTimeOffTypeMap = getKeyToTimeOffTypeMap(idToTimeOffTypeMap.values());

        Map<String, TimeoffEntitlementDBO> keyToExistingEntitlementsMap = getKeyToEntitlementMap(idToTimeOffTypeMap, existingEntitlements);
        Set<TimeoffEntitlementDBO> missingEntitlementDBOs = findMissingEntitlementsInInput(getKeys(inputs), keyToExistingEntitlementsMap);

        throwIfEORCannotUpdateEntitlements(contract, inputs, missingEntitlementDBOs, keyToTimeOffTypeMap, typeIdToAvailableDefinitionMap);

        // delete entitlements and summaries if there are missing entitlements
        if (!CollectionUtils.isEmpty(missingEntitlementDBOs)) {
            deleteEntitlements(missingEntitlementDBOs, contract.getId());
        }
        updateEntitlementsForEOR(inputs, keyToExistingEntitlementsMap, keyToTimeOffTypeMap, typeIdToAvailableDefinitionMap, contract.getId());
    }

    private Map<Long, TimeOffDefinitionEntity> toTypeIdToDefinitionMap(Map<Long, TimeOffDefinitionEntity> definitionIdToDefinitionMap) {
        return definitionIdToDefinitionMap.values().stream()
                .collect(Collectors.toMap(TimeOffDefinitionEntity::getTypeId, Function.identity()));
    }

    private void throwIfContractCannotUpdateEntitlements(ContractOuterClass.Contract contract) {
        throwIfUnauthorized(contract);
        if (!CONTRACT_TYPES_ELIGIBLE_FOR_TIMEOFFS.contains(contract.getType())) {
            throw new ValidationException(String.format("Add/edit entitlements not supported for contract type : %s (contract id : %d)" , contract.getType(), contract.getId()));
        }
    }

    private void deleteEntitlements(Set<TimeoffEntitlementDBO> timeoffEntitlementDBOs, Long contractId) {
        timeoffEntitlementDBORepository.deleteAll(timeoffEntitlementDBOs);
        List<Long> missingTypeIds = timeoffEntitlementDBOs.stream().map(TimeoffEntitlementDBO::typeId).toList();
        timeoffSummaryRepository.deleteAllByContractIdAndTypeIdIn(contractId, missingTypeIds);
    }

    private void processEntitlementsUpdateForHRMember(List<ContractUpdateTimeOffEntitlementsInput> inputs,
                                                      Map<String, TimeoffEntitlementDBO> existingEntitlementByKey) {
        inputs.forEach(inp -> {
            if (existingEntitlementByKey.containsKey(inp.getKey())) {
                var entitlementDBO = existingEntitlementByKey.get(inp.getKey());
                entitlementDBO.value(inp.getValue());
                entitlementDBO.unit(inp.getUnit());
                timeoffSummaryService.onTimeOffEntitlementChange(timeoffEntitlementDBORepository.saveAndFlush(entitlementDBO));
            }
        });
    }

    private void throwIfHrMemberCannotUpdateEntitlements(List<ContractUpdateTimeOffEntitlementsInput> inputs,
                                                         Map<String, TimeoffEntitlementDBO> existingEntitlementByKey,
                                                         ContractOuterClass.Contract contract,
                                                         Map<String, TimeoffTypeDBO> keyToTimeOffTypeMap,
                                                         Map<Long, TimeOffDefinitionEntity> typeIdToDefinitionMap) {
        // HR Member can only update entitlements, cannot add new entitlements or delete existing entitlements
        val newEntitlementsToAdd = inputs
                .stream()
                .filter(inp -> !existingEntitlementByKey.containsKey(inp.getKey()))
                .toList();
        if (!CollectionUtils.isEmpty(newEntitlementsToAdd)) {
            throw new ValidationException("Cannot add new entitlements for HR_MEMBER contract type (contract id :" + contract.getId()+ ")");
        }

        Set<TimeoffEntitlementDBO> missingEntitlementDBOs = findMissingEntitlementsInInput(getKeys(inputs), existingEntitlementByKey);
        if (!CollectionUtils.isEmpty(missingEntitlementDBOs)) {
            throw new ValidationException("Cannot delete entitlements for HR_MEMBER contract type (contract id :" + contract.getId() + ")");
        }

        throwIfEntitlementsAreOutOfLimits(inputs, contract, keyToTimeOffTypeMap, typeIdToDefinitionMap);
    }

    private Set<String> getKeys(List<ContractUpdateTimeOffEntitlementsInput> inputs) {
        return inputs.stream()
                .map(ContractUpdateTimeOffEntitlementsInput::getKey)
                .collect(Collectors.toSet());
    }

    private void updateEntitlementsForEOR(List<ContractUpdateTimeOffEntitlementsInput> inputs,
                                          Map<String, TimeoffEntitlementDBO> existingEntitlementByKey,
                                          Map<String, TimeoffTypeDBO> timeoffTypeDboByKey,
                                          Map<Long, TimeOffDefinitionEntity> typeIdToDefinitionMap,
                                          Long contractId) {
        inputs.forEach(inp -> {
            TimeoffEntitlementDBO entitlementDBO;
            if (existingEntitlementByKey.containsKey(inp.getKey())) {
                entitlementDBO = existingEntitlementByKey.get(inp.getKey());
                entitlementDBO.value(inp.getValue());
                entitlementDBO.unit(inp.getUnit());
            } else {
                // HR members can't add new entitlements, Hence HR member won't reach here
                val type = timeoffTypeDboByKey.get(inp.getKey());
                if (type == null) {
                    throw new DataCorruptionException("No type exist for key :" + inp.getKey());
                }
                val definition = typeIdToDefinitionMap.get(type.id());
                entitlementDBO = TimeoffEntitlementDBO.builder()
                        .type(timeoffTypeDboByKey.get(inp.getKey()))
                        .definition(definition == null ? null : definition.getDefinition())
                        .value(inp.getValue())
                        .unit(inp.getUnit())
                        .contractId(contractId)
                        .build();
            }
            timeoffSummaryService.onTimeOffEntitlementChange(timeoffEntitlementDBORepository.saveAndFlush(entitlementDBO));
        });
    }


    /**
     * Clear existing entitlements (if any) and generate default ones based on the country for this contract<br>
     * Call this in createContract() or changeCountry() or any mutation that affects timeoff entitlements<br>
     * Use this with caution! This is allowed only when contract status = ONBOARDING
     */
    @Transactional
    public void setEntitlementsToDefaultRequirements(Long contractId) {
        ContractOuterClass.Contract contract = contractServiceAdapter.findContractByContractId(contractId);

        throwIfUnauthorized(contract);
        throwIfContractNotValid(contract);

        if (!CONTRACT_TYPES_ELIGIBLE_FOR_TIMEOFFS.contains(contract.getType())) {
            log.info("[setEntitlementsToDefaultRequirements] Contract type {} is not eligible for timeoff entitlements for contract id : {}", contract.getType(), contract.getId());
            return;
        }
        resetEntitlementToDefault(contract);
    }

    @Transactional
    public void setEntitlementsToDefaultRequirementsBulk(List<Long> contractIds) {
        if (CollectionUtils.isEmpty(contractIds)) {
            log.info("[setEntitlementsToDefaultRequirementsBulk] No contracts to reset entitlements");
            return;
        }

        val contracts = contractServiceAdapter.getContractsByIdsAnyStatus(contractIds, true);
        val idToValidContractsMap = filterContractsForResetEntitlements(contracts);
        val validContractIds = idToValidContractsMap.keySet();
        if (CollectionUtils.isEmpty(validContractIds)) {
            log.info("[setEntitlementsToDefaultRequirements] No valid contracts to reset entitlements");
            return;
        }
        throwIfCannotResetEntitlementsBulk(idToValidContractsMap.values());
        val companyId = extractCompanyIdFromContracts(idToValidContractsMap.values());
        val availableDefinitionsByContractId = definitionService.findAvailableDefinitionsByContract(companyId, idToValidContractsMap.values());
        val idToTimeOffTypeMap = getIdToTimeOffTypeDBOMap(getTypeIds(availableDefinitionsByContractId.values()));

        deleteAllExistingEntitlements(validContractIds);
        val entitlements = createDefaultEntitlementsForContracts(idToValidContractsMap.values(), availableDefinitionsByContractId, idToTimeOffTypeMap);
        val savedEntitlements = timeoffEntitlementDBORepository.saveAllAndFlush(entitlements);
        timeoffSummaryService.onTimeOffEntitlementsResetBulk(savedEntitlements, idToValidContractsMap);
    }

    private void throwIfCannotResetEntitlementsBulk(Collection<ContractOuterClass.Contract> contracts) {
        val companyIds = contracts.stream()
                .map(ContractOuterClass.Contract::getCompanyId)
                .collect(Collectors.toSet());
        if (companyIds.size() != 1) {
            throw new ValidationException(String.format("Trying to reset entitlements for contracts from {%s} companies", companyIds.size()));
        }
    }

    private Long extractCompanyIdFromContracts(Collection<ContractOuterClass.Contract> contracts) {
        return contracts.stream()
                .map(ContractOuterClass.Contract::getCompanyId)
                .findFirst()
                .orElseThrow(() -> new ValidationException("Company id not found for the contracts"));
    }

    private List<TimeoffEntitlementDBO> createDefaultEntitlementsForContracts(Collection<ContractOuterClass.Contract> contracts,
                                                                              Map<Long, Map<Long, TimeOffDefinitionEntity>> typeIdToAvailableDefMapByContractId,
                                                                              Map<Long, TimeoffTypeDBO> idToTimeOffTypeMap) {

        return contracts.stream()
                .map(contract -> {
                    val availableDefinitions = typeIdToAvailableDefMapByContractId.getOrDefault(contract.getId(), Collections.emptyMap());
                    return createDefaultEntitlements(availableDefinitions, idToTimeOffTypeMap, contract);
                })
                .flatMap(Collection::stream)
                .toList();
    }

    private List<TimeoffEntitlementDBO> createDefaultEntitlements(Map<Long, TimeOffDefinitionEntity> availableDefinitions,
                                                                  Map<Long, TimeoffTypeDBO> idToTimeOffTypeMap,
                                                                  ContractOuterClass.Contract contract) {
        return availableDefinitions.entrySet().stream()
                .filter(entry -> shouldGenerateEntitlement(entry.getValue()))
                .map(entry -> {
                    val timeOffType = idToTimeOffTypeMap.get(entry.getKey());
                    if (timeOffType == null) {
                        log.warn("[createDefaultEntitlements] Data corruption Timeoff type not found for definition id : {}, type id : {}"
                                , entry.getValue().getId(), entry.getKey());
                        return null;
                    }
                    return createEntitlementForDefinition(timeOffType, entry.getValue(), contract);
                })
                .filter(Objects::nonNull)
                .toList();
    }

    private TimeoffEntitlementDBO createEntitlementForDefinition(@NotNull TimeoffTypeDBO timeOffType,
                                                                 @NotNull TimeOffDefinitionEntity timeOffDefinition,
                                                                 ContractOuterClass.Contract contract) {
        val definition = getDefinitionForEntitlement(timeOffDefinition, contract);
        val entitlementValue = getDefaultValue(timeOffDefinition.getDefinition());
        return TimeoffEntitlementDBO.builder()
                .type(timeOffType)
                .definitionId(Optional.ofNullable(definition).map(DefinitionEntity::getId).orElse(null)) // setting id to use in summary service
                .typeId(timeOffType.id()) // setting id to use in summary service
                .definition(definition)
                .value(entitlementValue.getValue())
                .unit(entitlementValue.getUnit())
                .contractId(contract.getId())
                .build();
    }

    private Set<Long> getTypeIds(Collection<Map<Long, TimeOffDefinitionEntity>> typeIdToDefMaps) {
        return typeIdToDefMaps.stream()
                .flatMap(map -> map.keySet().stream())
                .collect(Collectors.toSet());
    }

    private void deleteAllExistingEntitlements(Set<Long> contractIds) {
        timeoffEntitlementDBORepository.deleteAllByContractIdIn(contractIds);
        timeoffEntitlementDBORepository.flush();
    }

    private Map<Long, ContractOuterClass.Contract> filterContractsForResetEntitlements(List<ContractOuterClass.Contract> contracts) {
        log.info("[filterContractsForResetEntitlements] Validation starts for {} contracts ", contracts.size());
        List<ContractOuterClass.Contract> validContracts = filterAuthorizedContracts(contracts);
        validContracts = filterValidContracts(validContracts);
        log.info("[filterContractsForResetEntitlements] Found {} valid contracts", validContracts.size());
        return toContractMap(validContracts);
    }

    private List<ContractOuterClass.Contract> filterAuthorizedContracts(List<ContractOuterClass.Contract> contracts) {
        if (isSystemCall() || isOpsExperience()) {
            // we allow calls originated from system schedulers to pass
            return contracts;
        }


        return contracts.stream()
                .filter(contract -> {
                    if (isMyCompany(contract.getCompanyId())) {
                        return true;
                    }
                    log.info("[filterAuthorizedContracts] Access denied for user id : {} for the contract id : {}",
                            currentUser.getContext().getId(), contract.getId());
                    return false;
                })
                .toList();
    }

    private List<ContractOuterClass.Contract> filterValidContracts(List<ContractOuterClass.Contract> contracts) {
        return contracts.stream()
                .filter(this::isValidContract)
                .toList();
    }

    private boolean isValidContract(ContractOuterClass.Contract contract) {
        if (!CONTRACT_TYPES_ELIGIBLE_FOR_TIMEOFFS.contains(contract.getType())) {
            log.info("[filterValidContracts] Contract type {} is not eligible for timeoff entitlements for contract id : {}",
                    contract.getType(), contract.getId());
            return false;
        }
        if (contract.getStatus() != ContractOuterClass.ContractStatus.ONBOARDING) {
            log.info("[filterValidContracts] Contract status {} is not eligible for  reset timeoff entitlements for contract id : {}",
                    contract.getStatus(), contract.getId());
            return false;
        }
        return true;
    }


    private Map<Long, ContractOuterClass.Contract> toContractMap(List<ContractOuterClass.Contract> contracts) {
        return contracts.stream().collect(Collectors.toMap(ContractOuterClass.Contract::getId, Function.identity()));
    }

    /**
     * Clear existing entitlements (if any) and generate default ones based on the country for this contract<br>
     * Call this in createContract() or changeCountry() or any mutation that affects timeoff entitlements<br>
     * Use this with caution! This is allowed only when contract status = ONBOARDING
     */
    private void resetEntitlementToDefault(@NotNull ContractOuterClass.Contract contract) {
        log.info("[resetEntitlementToDefault] contractId={}", contract.getId());
        val contractId = contract.getId();
        clearExistingEntitlements(contractId);
        generateDefaultEntitlements(contract);
    }

    private void generateDefaultEntitlements(ContractOuterClass.Contract contract) {
        Map<Long, TimeOffDefinitionEntity> typeIdToAvailableDefMap = definitionService.findTypeIdToAvailableDefMapForContract(contract);
        val idToTimeOffTypeMap = getIdToTimeOffTypeDBOMap(typeIdToAvailableDefMap.keySet());
        List<TimeoffEntitlementDBO> entitlements = createDefaultEntitlements(typeIdToAvailableDefMap, idToTimeOffTypeMap, contract);
        val savedEntitlements = timeoffEntitlementDBORepository.saveAllAndFlush(entitlements);
        timeoffSummaryService.onTimeOffEntitlementsResetBulk(savedEntitlements, Map.of(contract.getId(), contract));
    }

    private void throwIfContractNotValid(ContractOuterClass.Contract contract) {
        if (contract == null) {
            throw new ValidationException("Contract not found to reset entitlements");
        }
        if (contract.getStatus() != ContractOuterClass.ContractStatus.ONBOARDING) {
            throw new ValidationException("Can clear existing and generate default timeoff entitlements ONLY when contract status is ONBOARDING. ContractID=" + contract.getId());
        }
    }

    private void clearExistingEntitlements(Long contractId) {
        // clear all existing entitlements and summaries
        timeoffEntitlementDBORepository.deleteAllByContractId(contractId);
        timeoffSummaryRepository.deleteAllByContractId(contractId);
        timeoffEntitlementDBORepository.flush();
    }

    private Map<String, TimeoffTypeDBO> getKeyToTimeOffTypeMap(Collection<TimeoffTypeDBO> timeoffTypeDBOs) {
        return timeoffTypeDBOs
                .stream()
                .collect(Collectors.toMap(TimeoffTypeDBO::key, Function.identity()));
    }

    private boolean shouldGenerateEntitlement(TimeOffDefinitionEntity timeOffDefinitionEntity) {
        val definition = timeOffDefinitionEntity.getDefinition();
        if (definition == null) {
            return false;
        }
        boolean requiredTimeOff = Boolean.TRUE.equals(definition.isRequired());
        val defaultValue = getDefaultValue(definition).getValue();
        boolean nonMandatoryTimeOffWithNonZeroDefaultValue = Boolean.FALSE.equals(definition.isRequired())
                && defaultValue != null
                && defaultValue > 0;
        return requiredTimeOff || nonMandatoryTimeOffWithNonZeroDefaultValue;
    }

    private @Nullable DefinitionEntity getDefinitionForEntitlement(TimeOffDefinitionEntity timeOffDefinitionEntity, ContractOuterClass.Contract contract) {
        if (contract.getType() == ContractOuterClass.ContractType.HR_MEMBER
                && isHRMemberDefaultEntitlement(timeOffDefinitionEntity)) {
            return null;
        }
        return timeOffDefinitionEntity.getDefinition();
    }

    private boolean isHRMemberDefaultEntitlement(TimeOffDefinitionEntity timeOffDefinitionEntity) {
        return HrMemberTimeOffDefinitionKt.getHrMemberTimeOffDefinitions().contains(timeOffDefinitionEntity);
    }


    private TimeOffDuration getDefaultValue(@Nullable DefinitionEntity definitionEntity) {
        if (definitionEntity == null) {
            return TimeOffDuration.newBuilder()
                    .value(0.0)
                    .unit(TimeOffUnit.DAYS)
                    .build();
        }
        return definitionEntity.getValidations().stream()
                .findFirst()
                .map(validation -> TimeOffDuration.newBuilder()
                        .value(validation.getDefaultValue())
                        .unit(validation.getUnit())
                        .build())
                .orElse(TimeOffDuration.newBuilder()
                        .value(0.0)
                        .unit(TimeOffUnit.DAYS)
                        .build());
    }



    @NotNull
    @Transactional(readOnly = true)
    public List<ContractTimeOffEntitlement> getTimeOffEntitlementsForContract(Long contractId) {
        // guaranteed to be a valid contractId by this method
        ContractOuterClass.Contract contract = contractServiceAdapter.getContractByIdAnyStatus(contractId);
        List<TimeoffEntitlementDBO> existingEntitlements = timeoffEntitlementDBORepository.findAllByContractId(contract.getId());
        Map<Long, TimeoffTypeDBO> idToTimeOffTypeMap = getIdToTimeOffTypeDBOMap(getTypeIdsFromEntitlements(existingEntitlements));
        Map<Long, DefinitionEntity> idToDefinitionEntityMap = getIdToDefinitionEntityMap(getDefinitionIds(existingEntitlements));
        Map<Long, CountryDefinitionEntity> typeIdToCountryDefinitionMap = definitionService.getTypeIdToEligibleCountryDefinitionMap(contract);

        return existingEntitlements.stream()
                .map(ent -> {
                    val definition = idToDefinitionEntityMap.get(ent.definitionId());
                    val type = idToTimeOffTypeMap.get(ent.typeId());
                    return ContractTimeOffEntitlement.newBuilder()
                            .definition(timeOffTypeDefinitionMapper.map(definition, type))
                            .value(ent.value())
                            .unit(ent.unit())
                            .timeOffType(timeoffTypeMapper.mapToGraph(type))
                            .isMandatory(isMandatoryEntitlement(typeIdToCountryDefinitionMap.get(ent.typeId()), definition))
                            .build();
                })
                .collect(Collectors.toList());
    }

    private Map<Long, DefinitionEntity> getIdToDefinitionEntityMap(Set<Long> definitionIds) {
        return definitionEntityRepository.findAllById(definitionIds)
                .stream()
                .collect(Collectors.toMap(DefinitionEntity::getId, Function.identity()
                ));
    }

    private Set<Long> getDefinitionIds(List<TimeoffEntitlementDBO> timeoffEntitlementDBOs) {
        return timeoffEntitlementDBOs.stream()
                .map(TimeoffEntitlementDBO::definitionId)
                .collect(Collectors.toSet());
    }

    private boolean isMandatoryEntitlement(@Nullable CountryDefinitionEntity countryDefinitionEntity, DefinitionEntity definitionForEntitlement) {
        if (countryDefinitionEntity == null) {
            // if no country definition -> then entitlement is from the company definition
            return definitionForEntitlement != null && definitionForEntitlement.isRequired();
        }
        return countryDefinitionEntity.getDefinition() != null && countryDefinitionEntity.getDefinition().isRequired();
    }

    private Map<Long, TimeoffTypeDBO> getIdToTimeOffTypeDBOMap(Set<Long> typeIds) {
        return timeoffTypeRepository.findAllById(typeIds)
                .stream()
                .collect(Collectors.toMap(
                        TimeoffTypeDBO::id,
                        Function.identity()));
    }

    private Set<Long> getTypeIdsFromEntitlements(List<TimeoffEntitlementDBO> entitlementDBOs) {
        return entitlementDBOs.stream()
                .map(TimeoffEntitlementDBO::typeId)
                .collect(Collectors.toSet());
    }

    @Async
    public void backFillDefinitionIdsForEntitlements(Long companyId) {
        log.info("[backFillDefinitionIdsForEntitlements] Process started");
        val pageSize= 5000;
        org.springframework.data.domain.Sort sort = org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.ASC, "id");

        val allCountryDefinitions = countryDefinitionRepository.findAll();

        BackFillEntitlementDefinitionIdResult backFillEntitlementDefinitionIdResult = BackFillEntitlementDefinitionIdResult.builder()
                .successCount(0L)
                .foundCount(0L)
                .failedEntitlementIds(new ArrayList<>())
                .contractNotFoundIds(new ArrayList<>())
                .processedIds(new HashSet<>())
                .build();
        List<ContractOuterClass.Contract> contracts = companyId == null ? List.of() : contractServiceAdapter.getAllContractsForCompany(companyId);
        Set<Long> contractIds = contracts.stream().map(ContractOuterClass.Contract::getId).collect(Collectors.toSet());
        Page<TimeoffEntitlementDBO> entitlementPage;
        do  {
            org.springframework.data.domain.PageRequest pageRequest = org.springframework.data.domain.PageRequest.of(0, pageSize, sort);

            val spec = timeoffEntitlementSpecificationBuilder.buildForContractIdIn(contractIds, backFillEntitlementDefinitionIdResult.processedIds);
            entitlementPage = timeoffEntitlementDBORepository.findAll(spec, pageRequest);

            log.info("=================== back filling started for entries {} (Processed - {}/{} success - {} failed - {}) ===================",
                    entitlementPage.getNumberOfElements(),
                    backFillEntitlementDefinitionIdResult.processedIds.size(),
                    backFillEntitlementDefinitionIdResult.processedIds.size() + entitlementPage.getTotalElements(),
                    backFillEntitlementDefinitionIdResult.successCount,
                    backFillEntitlementDefinitionIdResult.failedEntitlementIds.size() + backFillEntitlementDefinitionIdResult.contractNotFoundIds.size());
            backFillForPage(entitlementPage, allCountryDefinitions, backFillEntitlementDefinitionIdResult);
        } while (!entitlementPage.isEmpty());

        val totalFailedEntitlementSize = backFillEntitlementDefinitionIdResult.failedEntitlementIds.size() + backFillEntitlementDefinitionIdResult.contractNotFoundIds.size();
        log.info("Found : {} success : {} : failed : {} contract not found for : {}", backFillEntitlementDefinitionIdResult.foundCount,
                backFillEntitlementDefinitionIdResult.successCount, totalFailedEntitlementSize, backFillEntitlementDefinitionIdResult.contractNotFoundIds.size());

        if (totalFailedEntitlementSize > 0) {
            log.info("""
                            [backFillDefinitionIdsForEntitlements] Failure summary. contracts not found : {}/{}, failed : {}/{}\s
                             Failed Entitlement ids : {}
                             Contract not found ids : {}""",
                    backFillEntitlementDefinitionIdResult.contractNotFoundIds.size(), totalFailedEntitlementSize, backFillEntitlementDefinitionIdResult.failedEntitlementIds.size(),
                    totalFailedEntitlementSize, backFillEntitlementDefinitionIdResult.failedEntitlementIds, backFillEntitlementDefinitionIdResult.contractNotFoundIds);
        }
        log.info("[backFillDefinitionIdsForEntitlements] Process completed");
    }

    private void backFillForPage(Page<TimeoffEntitlementDBO> entitlementPage, List<CountryDefinitionEntity> allCountryDefinitions,
                                 BackFillEntitlementDefinitionIdResult backFillEntitlementDefinitionIdResult) {

        List<TimeoffEntitlementDBO> entitlementDBOs = entitlementPage.getContent();
        if (CollectionUtils.isEmpty(entitlementDBOs)) {
            log.info("No entitlement found to back fill");
            return;
        }
        backFillEntitlementDefinitionIdResult.setFoundCount(backFillEntitlementDefinitionIdResult.foundCount + entitlementDBOs.size());

        val idToContractMap = contractServiceAdapter.getContractsByIdsAnyStatus(getContractIds(entitlementDBOs), true).stream()
                .collect(Collectors.toMap(ContractOuterClass.Contract::getId, Function.identity()));
        val typeIds = entitlementDBOs.stream()
                .map(TimeoffEntitlementDBO::typeId)
                .collect(Collectors.toSet());
        val companyIds = entitlementDBOs.stream()
                .map(ent -> idToContractMap.get(ent.contractId()))
                .filter(Objects::nonNull)
                .map(ContractOuterClass.Contract::getCompanyId)
                .collect(Collectors.toSet());
        val companyIdToTypeIdToDefinitionMap = companyDefinitionRepository.findAllNonDeletedByTypeIdInAndCompanyIdIn(typeIds, companyIds).stream()
                .filter(cd -> cd != null && cd.getCompanyId() != null)
                .collect(Collectors.groupingBy(
                        CompanyDefinitionEntity::getCompanyId,
                        Collectors.groupingBy(CompanyDefinitionEntity::getTypeId)
                ));
        val typeIdToDefaultDefinitions = allCountryDefinitions.stream()
                .filter(cd -> cd.getTypeId() != null && cd.getDefinition() != null)
                .filter(cd -> cd.getDefinition().getCountryCode() == null && (StringUtils.isEmpty(cd.getDefinition().getStateCode())))
                .collect(Collectors.groupingBy(CountryDefinitionEntity::getTypeId));
        val countryDefinitions = allCountryDefinitions.stream()
                .filter(cd -> cd.getTypeId() != null && cd.getDefinition() != null && cd.getDefinition().getCountryCode() != null)
                .collect(Collectors.groupingBy(
                                CountryDefinitionEntity::getTypeId,
                                Collectors.groupingBy(cd -> cd.getDefinition().getCountryCode())
                        )
                );

        for (TimeoffEntitlementDBO timeoffEntitlementDBO : entitlementDBOs) {
            backFillEntitlementDefinitionIdResult.processedIds.add(timeoffEntitlementDBO.id);
            val contract = idToContractMap.get(timeoffEntitlementDBO.contractId());
            if (contract == null) {
                backFillEntitlementDefinitionIdResult.contractNotFoundIds.add(timeoffEntitlementDBO.id);
                continue;
            }
            val companyId = contract.getCompanyId();
            val typeId = timeoffEntitlementDBO.typeId();

            try {
                if (contract.getType() == ContractOuterClass.ContractType.HR_MEMBER) {
                    log.info("[backFillForPage] no back filling required for HR_MEMBER (contract_id = {})", contract.getId());
                    backFillEntitlementDefinitionIdResult.successCount++;
                    continue;
                }
                var foundedDefinition = findByCompanyDefinitions(companyIdToTypeIdToDefinitionMap.getOrDefault(companyId, Map.of()), timeoffEntitlementDBO);
                if (foundedDefinition == null) {
                    log.info("[backFillForPage] No company definitions found for entitlement id : {} ===> Searching for country definitions", timeoffEntitlementDBO.id());
                    foundedDefinition = findByCountryDefinitions(countryDefinitions, timeoffEntitlementDBO, contract);
                    if (foundedDefinition == null) {
                        log.info("[backFillForPage] No country definitions found for entitlement id : {} ===> Searching for default definitions", timeoffEntitlementDBO.id());
                        foundedDefinition = findByDefaultDefinitions(typeIdToDefaultDefinitions.get(typeId), timeoffEntitlementDBO);
                    }
                }

                if (foundedDefinition != null) {
                    timeoffEntitlementDBO.definition(foundedDefinition);
                    backFillEntitlementDefinitionIdResult.successCount++;
                } else {
                    backFillEntitlementDefinitionIdResult.failedEntitlementIds.add(timeoffEntitlementDBO.id());
                }
            } catch (Exception e) {
                // have to handle these cases manually
                backFillEntitlementDefinitionIdResult.failedEntitlementIds.add(timeoffEntitlementDBO.id());
                log.error("[backFillDefinitionIdsForEntitlements] Failed to update entitlement id : {} company id : {}, due to : ", timeoffEntitlementDBO.id(), companyId, e);
            }
        }

        timeoffEntitlementDBORepository.saveAllAndFlush(entitlementDBOs);
    }

    private @Nullable DefinitionEntity findByCompanyDefinitions(Map<Long, List<CompanyDefinitionEntity>> typeIdToDefinitionMap,
                                                                TimeoffEntitlementDBO timeoffEntitlementDBO) {
        val companyDefinitions = typeIdToDefinitionMap.get(timeoffEntitlementDBO.typeId());
        if (CollectionUtils.isEmpty(companyDefinitions)) {
            return null;
        }

        return companyDefinitions.stream()
                .filter(cd -> cd.getCreatedOn() != null)
                .min(Comparator.comparing(CompanyDefinitionEntity::getCreatedOn))
                .map(CompanyDefinitionEntity::getDefinition)
                .orElse(null);

    }

    private @Nullable DefinitionEntity findByCountryDefinitions(Map<Long, Map<CountryCode, List<CountryDefinitionEntity>>> typeIdToCountryDefinitions,
                                                                TimeoffEntitlementDBO timeoffEntitlementDBO,
                                                                ContractOuterClass.Contract contract) {
        val countryCode = CountryCode.valueOf(contract.getCountry());
        val stateCode = contract.getCountryStateCode();
        val allCountryDefinitionsForType = typeIdToCountryDefinitions.getOrDefault(timeoffEntitlementDBO.typeId(), Map.of());

        if (CollectionUtils.isEmpty(allCountryDefinitionsForType)) {
            log.info("[findByCountryDefinitions] No country definitions found for entitlement id : {} and type id : {}",
                    timeoffEntitlementDBO.id(), timeoffEntitlementDBO.typeId());
            return null;
        }

        val countryDefinitions = allCountryDefinitionsForType.getOrDefault(countryCode, List.of());
        if (StringUtils.isBlank(stateCode)) {
            val definition = getCountryDefinition(countryDefinitions);
            if (definition == null) {
                log.info("[findByCountryDefinitions] No country definition found for entitlement id : {}, type id : {}, country : {}, state : null",
                        timeoffEntitlementDBO.id(), timeoffEntitlementDBO.typeId(), countryCode);
                return null;
            }
            return definition.getDefinition();
        }

        // find definition by country and state
        var definition = countryDefinitions.stream()
                .filter(cd -> !StringUtils.isEmpty(cd.getDefinition().getStateCode()))
                .filter(cd -> cd.getDefinition().getStateCode().equalsIgnoreCase(stateCode))
                .findFirst()
                .orElse(null);
        if (definition != null) {
            return definition.getDefinition();
        }

        log.info("[findByCountryDefinitions] No country-state definition found for entitlement id : {}, type Id : {}, country : {}, state : {} ==> Searching for country definition",
                timeoffEntitlementDBO.id(), timeoffEntitlementDBO.typeId(), countryCode, stateCode);
        definition = getCountryDefinition(countryDefinitions);
        if (definition == null) {
            log.info("[findByCountryDefinitions] No country definition found for entitlement id : {}, type id : {}, country : {}, state : null",
                    timeoffEntitlementDBO.id(), timeoffEntitlementDBO.typeId(), countryCode);
            return null;
        }
        return definition.getDefinition();
    }

    private @Nullable DefinitionEntity findByDefaultDefinitions(List<CountryDefinitionEntity> defaultDefinitions, TimeoffEntitlementDBO timeoffEntitlementDBO) {
        if (CollectionUtils.isEmpty(defaultDefinitions)) {
            log.info("[findByDefaultDefinitions] No default definitions found for entitlement id : {}, type Id : {}", timeoffEntitlementDBO.id(), timeoffEntitlementDBO.typeId());
            return null;
        }
        if (defaultDefinitions.size() > 1) {
            log.info("[backFillDefinitionIdsForEntitlements] Multiple default definitions found for entitlement id : {}. type id : {}", timeoffEntitlementDBO.id(), timeoffEntitlementDBO.typeId());
            return null;
        }
        return defaultDefinitions.get(0).getDefinition();
    }


    private CountryDefinitionEntity getCountryDefinition(@NotNull List<CountryDefinitionEntity> countryDefinitions) {
        return countryDefinitions.stream()
                .filter(cd -> cd.getDefinition() != null && StringUtils.isBlank(cd.getDefinition().getStateCode()))
                .findFirst()
                .orElse(null);
    }

    private Set<Long> getContractIds(List<TimeoffEntitlementDBO> entitlementDBOS) {
        return entitlementDBOS.stream().map(TimeoffEntitlementDBO::contractId).collect(Collectors.toSet());
    }

    @Transactional
    @Async
    public void reallocateTimeoff(LocalDate expiryTime) {
        List<TimeoffSummaryDBO> summariesForReallocation = timeoffSummaryService.getLatestSummariesExpiredBeforeOrOn(expiryTime);
        log.info("[reallocateTimeoff] Reallocation Time off summary on={} for={}", LocalDateTime.now(), summariesForReallocation.size());
        Map<Long, ContractOuterClass.Contract> contractById = contractServiceAdapter.getNonDeletedNonEndedContractsByIdsInChunks(
                summariesForReallocation.stream().map(TimeoffSummaryDBO::contractId).collect(Collectors.toList())
        ).stream().collect(Collectors.toMap(
                ContractOuterClass.Contract::getId,
                Function.identity()
        ));

        Map<Long, Boolean> companyIdToFutureLeavesEnabledMap = getCompanyIdToFutureLeavesEnabledMap(
                getCompanyIds(contractById.values())
        );

        var generatedSummaries = 0;
        for (TimeoffSummaryDBO summary : summariesForReallocation) {
            try {
                val contract = contractById.get(summary.contractId());
                if (contract == null) {
                    log.info("Skip timeoff reallocation for summary ID = {}, contract ID = {}: non deleted, non ended contract not found", summary.id, summary.contractId());
                    continue;
                }

                // skip if the new "future leaves" feature is enabled.
                if (isFutureLeaveFeatureEnabled(contract, companyIdToFutureLeavesEnabledMap)) {
                    log.info("[reallocateTimeoff] Skip timeoff reallocation for summary ID = {}, contract ID = {}: New [future leaves] feature is enabled for company", summary.id, summary.contractId());
                    continue;
                }

                timeoffSummaryService.legacyReallocateTimeoff(summary, contract);
                generatedSummaries++;
            } catch (Exception e) {
                log.warn("[TimeoffService] Reallocation of timeoff threw exception={}", e.getMessage());
            }
            log.info("[reallocateTimeoff] Timeoff summaries re-allocated for = {}", generatedSummaries);

        }
        log.info("[reallocateTimeoff] reallocation timeoff job completed. Generated summary count = {}", generatedSummaries);
    }

    private Set<Long> getCompanyIds(Collection<ContractOuterClass.Contract> contracts) {
        return contracts.stream()
                .map(ContractOuterClass.Contract::getCompanyId)
                .collect(Collectors.toSet());
    }

    private Map<Long, Boolean> getCompanyIdToFutureLeavesEnabledMap(Set<Long> companyIds) {
        if (CollectionUtils.isEmpty(companyIds)) {
            return Collections.emptyMap();
        }
        return companyIds.stream()
                .collect(Collectors.toMap(
                        Function.identity(),
                        companyId -> featureFlagService.isOn(FeatureFlags.FUTURE_LEAVES, Map.of("company", companyId))));
    }

    private boolean isFutureLeaveFeatureEnabled(ContractOuterClass.Contract contract, Map<Long, Boolean> companyIdToFutureLeavesEnabledMap) {
        if (contract == null) {
            return false;
        }
        return companyIdToFutureLeavesEnabledMap.getOrDefault(contract.getCompanyId(), false);
    }

    @Transactional
    public void reRunAllocationTimeOff(Long contractId) {
        log.info("[TimeoffService] Rerunning the timeoff allocation for contractID={}", contractId);

        ContractOuterClass.Contract contract = contractServiceAdapter.findContractByContractId(contractId);
        throwIfUnauthorized(contract);
        List<TimeoffEntitlementDBO> existingEntitlements = timeoffEntitlementDBORepository.findAllByContractId(contractId);
        existingEntitlements.forEach(timeoffSummaryService::onTimeOffEntitlementChange);
        log.info("[TimeoffService] The Rerunning of timeoff allocation is successful for contractId={}", contractId);
    }

    /**
     * Should be used in member exp only.
     *
     * @return Contract info of the currentUser
     */
    private ContractOuterClass.Contract getCurrentUserContract() {
        var memberId = currentUser.getContext().getScopes().getMemberId();

        if (memberId == null) {
            throw new EntityNotFoundException("Cannot find contract because there is no member id");
        }

        ContractOuterClass.Contract contractByMemberId = contractServiceAdapter.findContractByMemberId(memberId);

        if (contractByMemberId == null) {
            throw new EntityNotFoundException("Cannot find contract for member id = " + memberId);
        }

        return contractByMemberId;
    }

    private boolean isCreatedByUndefined(TimeoffDBO timeoffDBO) {
        return timeoffDBO.createdBy() == null || timeoffDBO.createdBy() == -1;
    }

    private TimeoffDBO getTargetTimeoffDBO(Long id) {
        if (id == null) {
            return new TimeoffDBO();
        }

        return getOrThrowTimeoffById(id);
    }

    private boolean isNewTimeoff(TimeoffDBO timeoffDBO) {
        return timeoffDBO.id() == null;
    }

    @Transactional
    public void onTimeOffApprovalStatusChange(@NotNull GrpcTimeOffApprovalStatusChangeEvent request) {
        String experience = currentUser.getContext() == null ? null : currentUser.getContext().getExperience();
        log.info("[onTimeOffApprovalStatusChange] with experience={}, request={}", experience, request);
        TimeoffDBO timeoff;
        switch (request.getStatus()) {
            case APPROVED:
                log.info("Timeoff approved for timeoffID={}.", request.getTimeoffId());
                timeoff = updateTimeoffStatus(request.getTimeoffId(), TimeOffStatus.APPROVED, request.getComment());
                timeoffKafkaPublisher.publishTimeoffUpdateEvent(mapper.map(timeoff));
                timeoffNotification.sendTimeoffApprovedEmailToMember(timeoff);
                break;

            case APPROVAL_IN_PROGRESS:
                log.info("Timeoff approval in progress for timeoffID={}.", request.getTimeoffId());

                timeoff = updateTimeoffStatus(request.getTimeoffId(), APPROVAL_IN_PROGRESS, request.getComment());
                timeoffKafkaPublisher.publishTimeoffUpdateEvent(mapper.map(timeoff));
                if (shouldSendTimeOffCreatedEmailToApprover(experience, request)) {
                    var approver = companyServiceAdapter.getCompanyUser(new CompanyUserFilters(request.getApproverCompanyUserId(), null, null));
                    log.info("Sending an email to company user userId={}", approver.getUserId());
                    timeoffNotification.sendTimeoffCreatedEmailToApprover(timeoff, approver);
                }
                break;

            case REJECTED:
                log.info("Timeoff rejected for timeoffID={}, due to {}.", request.getTimeoffId(), request.getComment());
                timeoff = updateTimeoffStatus(request.getTimeoffId(), TimeOffStatus.REJECTED, request.getComment());
                timeoffKafkaPublisher.publishTimeoffUpdateEvent(mapper.map(timeoff));
                timeoffNotification.sendTimeoffRejectedEmailToMember(timeoff);
                break;

            case ACTION_TAKEN:
                log.info("Timeoff approval action taken for timeoffID={}, due to {}.", request.getTimeoffId(), request.getComment());
                break;

            default:
                log.warn("Status not handled...");
        }
    }

    private boolean shouldSendTimeOffCreatedEmailToApprover(@Nullable String experience, GrpcTimeOffApprovalStatusChangeEvent event) {
        return MEMBER_EXP.equals(experience) || event.getIsForwarding();
    }

    // how can we make timeoff-service aware of `currentUser` (i.e., Security context) being used from the grpc caller side (in this case core-service)?
    // It can't, the @Audit/@PrePersist/@PreUpdate on created_by/created_by_info etc...will fail (or null will be saved)
    private TimeoffDBO updateTimeoffStatus(Long timeoffId, TimeOffStatus status, String comment) {

        TimeoffDBO timeoff = timeoffRepository.findById(timeoffId)
                .orElseThrow(() -> new EntityNotFoundException("Cannot find timeoff for id = " + timeoffId));

        timeoff.status(status)
                .changeReason(StringUtils.isBlank(comment) ? null : comment.trim());

        if (status == TimeOffStatus.APPROVED) {
            timeoff.approvedOn(Instant.now());
        }

        timeoffRepository.saveAndFlush(timeoff);
        timeoffSummaryService.onTimeOffChange(timeoff);

        return timeoff;
    }


    @Transactional(readOnly = true)
    @Deprecated
    public List<GrpcTimeOffForPayroll> getTimeOffsForPayroll(Set<Long> contractIds, Set<TimeOffStatus> statuses,
                                                             LocalDate startDate, LocalDate endDate, Instant approvedOnGreaterThanEqTo) {
        log.info("Called TimeoffService.getTimeOffsForPayroll");

        val timeOffs = timeoffRepository.findAllForPayroll(contractIds, statuses, startDate, endDate, approvedOnGreaterThanEqTo);

        if (timeOffs.isEmpty()) {
            return Collections.emptyList();
        }

        return grpcTimeOffForPayrolls(contractIds, timeOffs, startDate, endDate);
    }

    private Map<Pair<Integer, Long>, List<LegalEntityHoliday.Holiday>> getHolidaysByEntity(Set<Integer> yearsList, Set<Long> contractIdsToFetchHolidayByEntity) {
        return yearsList.stream()
                .flatMap(year -> holidayServiceAdapter.getHolidays(contractIdsToFetchHolidayByEntity, year, null).stream())
                .flatMap(holiday -> holiday.getContractIdsList().stream()
                        .map(contractId -> Pair.of(holiday.getYear(), contractId))
                        .map(pair -> new AbstractMap.SimpleEntry<>(pair, holiday)))
                .collect(Collectors.groupingBy(Map.Entry::getKey,
                        Collectors.mapping(Map.Entry::getValue, Collectors.toList())));
    }

    private Map<Pair<Integer, String>, List<HolidayOuterClass.Holiday>> getHolidaysByCountry(Set<Integer> yearsList, Set<Country.GrpcCountryCode> countryCodes) {
        return yearsList.stream()
                .map(year -> holidayServiceAdapter.getHolidays(countryCodes, year, null, null))
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(holiday -> Pair.of(holiday.getYear(), holiday.getCountryCode().name())));
    }

    private boolean isHolidayByEntityFeatureEnabled(Long companyId) {
        return featureFlagService.feature(FeatureFlags.HOLIDAYS_BY_ENTITY, Map.of(COMPANY_EXP, companyId)).on();
    }


    @Transactional(readOnly = true)
    public List<GrpcTimeOffForPayroll> getTimeOffsForPayrollCycle(Set<Long> contractIds, Set<TimeOffStatus> statuses,
                                                                  LocalDate startDate, LocalDate endDate, LocalDate approvedOnFromInclusive, LocalDate approvedOnToInclusive) {
        log.info("Called TimeoffService.getTimeOffsForPayroll");

        var timeOffs = timeoffRepository.findAllForPayrollCycle(contractIds, statuses, startDate, endDate, approvedOnFromInclusive, approvedOnToInclusive);

        if (timeOffs.isEmpty()) {
            return Collections.emptyList();
        }
        return grpcTimeOffForPayrolls(contractIds, timeOffs, startDate, endDate);
    }

    private List<GrpcTimeOffForPayroll> grpcTimeOffForPayrolls(Set<Long> contractIds, List<TimeoffDBO> timeOffs, LocalDate startDate, LocalDate endDate) {
        val idToContractsMap = getIdToContractMap(contractIds);
        Set<Integer> yearsListToFetchHolidaysByCountry = new HashSet<>();
        Set<Integer> yearsListToFetchHolidaysByEntity = new HashSet<>();
        Map<Long, ContractOuterClass.Contract> idToContractMapToFetchHolidaysByCountry = new HashMap<>();
        Map<Long, ContractOuterClass.Contract> idToContractMapToFetchHolidaysByEntity = new HashMap<>();
        List<TimeoffDBO> timeOffsToFetchHolidaysByCountry = new ArrayList<>();
        List<TimeoffDBO> timeOffsToFetchHolidaysByEntity = new ArrayList<>();

        timeOffs.forEach(timeOff -> {
            val contract = idToContractsMap.get(timeOff.contractId());
            if (contract == null) {
                log.warn(CONTRACT_NOT_FOUND_MESSAGE, timeOff.id());
                return;
            }
            if (isHolidayByEntityFeatureEnabled(contract.getCompanyId())) {
                timeOffsToFetchHolidaysByEntity.add(timeOff);
                idToContractMapToFetchHolidaysByEntity.put(timeOff.contractId(), contract);
                yearsListToFetchHolidaysByEntity.add(timeOff.startDate().getYear());
                yearsListToFetchHolidaysByEntity.add(timeOff.endDate().getYear());
            } else {
                timeOffsToFetchHolidaysByCountry.add(timeOff);
                idToContractMapToFetchHolidaysByCountry.put(timeOff.contractId(), contract);
                yearsListToFetchHolidaysByCountry.add(timeOff.startDate().getYear());
                yearsListToFetchHolidaysByCountry.add(timeOff.endDate().getYear());
            }
        });

        val grpcTimeOffsForHolidayByCountry = getTimeoffsForHolidayByCountry(timeOffsToFetchHolidaysByCountry, startDate, endDate, yearsListToFetchHolidaysByCountry, idToContractMapToFetchHolidaysByCountry);
        val grpcTimeOffsForHolidayByEntity = getTimeoffsForHolidayByEntity(timeOffsToFetchHolidaysByEntity, startDate, endDate, yearsListToFetchHolidaysByEntity, idToContractMapToFetchHolidaysByEntity);
        val grpcTimeoffs = Stream.concat(grpcTimeOffsForHolidayByCountry.stream(), grpcTimeOffsForHolidayByEntity.stream())
                .collect(Collectors.toList());
        log.info("Found {} timeoffs in TimeoffService.getTimeOffsForPayroll", grpcTimeoffs.size());

        return grpcTimeoffs;
    }

    private List<GrpcTimeOffForPayroll> getTimeoffsForHolidayByCountry(List<TimeoffDBO> timeOffs,
                                                                       LocalDate startDate,
                                                                       LocalDate endDate,
                                                                       Set<Integer> yearsList,
                                                                       Map<Long, ContractOuterClass.Contract> idToContractMap) {

        if (timeOffs.isEmpty()) {
            return Collections.emptyList();
        }

        val countryCodes = getContractCountryCodes(idToContractMap.values());

        val holidaysByYearCountry = getHolidaysByCountry(yearsList, countryCodes);
        return timeOffs.stream()
                .map(timeOff -> {
                    val contract = idToContractMap.get(timeOff.contractId());
                    if (contract == null) {
                        log.warn(CONTRACT_NOT_FOUND_MESSAGE, timeOff.id());
                        return null;
                    }
                    val noOfDaysWithinCycle = calculateTimeOffInCutOffDateRangeWithCountryHolidays(timeOff,
                            startDate,
                            endDate,
                            contract.getCountry(),
                            holidaysByYearCountry);
                    return grpcTimeoffMapper.mapToGrpcTimeOffForPayroll(timeOff, noOfDaysWithinCycle);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private List<GrpcTimeOffForPayroll> getTimeoffsForHolidayByEntity(List<TimeoffDBO> timeOffs,
                                                                      LocalDate startDate,
                                                                      LocalDate endDate,
                                                                      Set<Integer> yearsList,
                                                                      Map<Long, ContractOuterClass.Contract> idToContractMap) {

        if (timeOffs.isEmpty()) {
            return Collections.emptyList();
        }

        val holidaysByYearContract = getHolidaysByEntity(yearsList, idToContractMap.keySet());
        return timeOffs.stream()
                .map(timeOff -> {
                    val contract = idToContractMap.get(timeOff.contractId());
                    if (contract == null) {
                        log.warn(CONTRACT_NOT_FOUND_MESSAGE, timeOff.id());
                        return null;
                    }
                    val noOfDaysWithinCycle = calculateTimeOffInCutOffDateRangeWithEntityHolidays(timeOff,
                            startDate,
                            endDate,
                            holidaysByYearContract);
                    return grpcTimeoffMapper.mapToGrpcTimeOffForPayroll(timeOff, noOfDaysWithinCycle);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private Set<Country.GrpcCountryCode> getContractCountryCodes(Collection<ContractOuterClass.Contract> contracts) {
        return contracts.stream()
                .map(contract -> Country.GrpcCountryCode.valueOf(contract.getCountry()))
                .collect(Collectors.toSet());
    }

    private Map<Long, ContractOuterClass.Contract> getIdToContractMap(Collection<Long> contractIds) {
        return contractServiceAdapter.getContractsByIdsAnyStatus(contractIds, true).stream()
                .collect(Collectors.toMap(ContractOuterClass.Contract::getId, Function.identity()));
    }

    private double calculateTimeOffInCutOffDateRangeWithCountryHolidays(TimeoffDBO timeOff,
                                                                        LocalDate startDateCutoff,
                                                                        LocalDate endDateCutoff,
                                                                        String countryCode,
                                                                        Map<Pair<Integer, String>, List<HolidayOuterClass.Holiday>> holidaysByYearCountry) {
        val timeOffStartDate = timeOff.startDate();
        val timeOffEndDate = timeOff.endDate();
        if (timeOffStartDate.isAfter(endDateCutoff) || timeOffEndDate.isBefore(startDateCutoff)) {
            // No Overlap
            return 0.0;
        }

        var noOfDays = 0.0;
        val laterStart = Collections.max(Arrays.asList(timeOffStartDate, startDateCutoff));
        val earlierEnd = Collections.min(Arrays.asList(timeOffEndDate, endDateCutoff));
        for (LocalDate date = laterStart; isBeforeOrEqual(date, earlierEnd); date = date.plusDays(1)) {
            if (WEEKENDS.contains(date.getDayOfWeek())) {
                continue;
            }
            var holidays = holidaysByYearCountry.get(Pair.of(date.getYear(), countryCode));
            if (!isCountryHolidayAPublicHoliday(date, holidays)) {
                noOfDays++;
            }
        }

        if (timeOffStartDate.isEqual(laterStart) && timeOff.startSession() == TimeOffSession.AFTERNOON) {
            noOfDays -= 0.5;
        }

        if (timeOffEndDate.isEqual(earlierEnd) && timeOff.endSession() == TimeOffSession.MORNING) {
            noOfDays -= 0.5;
        }

        return noOfDays;
    }

    private double calculateTimeOffInCutOffDateRangeWithEntityHolidays(TimeoffDBO timeOff, LocalDate startDateCutoff, LocalDate endDateCutoff,
                                                                       Map<Pair<Integer, Long>, List<LegalEntityHoliday.Holiday>> holidaysByYearContract) {
        val timeOffStartDate = timeOff.startDate();
        val timeOffEndDate = timeOff.endDate();
        if (timeOffStartDate.isAfter(endDateCutoff) || timeOffEndDate.isBefore(startDateCutoff)) {
            // No Overlap
            return 0.0;
        }

        var noOfDays = 0.0;
        val laterStart = Collections.max(Arrays.asList(timeOffStartDate, startDateCutoff));
        val earlierEnd = Collections.min(Arrays.asList(timeOffEndDate, endDateCutoff));
        for (LocalDate date = laterStart; isBeforeOrEqual(date, earlierEnd); date = date.plusDays(1)) {
            if (WEEKENDS.contains(date.getDayOfWeek())) {
                continue;
            }
            var holidays = holidaysByYearContract.get(Pair.of(date.getYear(), timeOff.contractId()));
            if (!isEntityHolidayAPublicHoliday(date, holidays)) {
                noOfDays++;
            }
        }

        if (timeOffStartDate.isEqual(laterStart) && timeOff.startSession() == TimeOffSession.AFTERNOON) {
            noOfDays -= 0.5;
        }

        if (timeOffEndDate.isEqual(earlierEnd) && timeOff.endSession() == TimeOffSession.MORNING) {
            noOfDays -= 0.5;
        }

        return noOfDays;
    }

    private boolean isBeforeOrEqual(LocalDate d1, LocalDate d2) {
        if (d1 == null || d2 == null) {
            return false;
        } else {
            return d1.isBefore(d2) || d1.isEqual(d2);
        }
    }


    private boolean isStartedLegacy(ContractOuterClass.Contract contract) {
        return contract.getStatus() == ContractOuterClass.ContractStatus.ACTIVE || contract.getStatus() == ContractOuterClass.ContractStatus.OFFBOARDING;
    }

    public void backfillTimeoffApprovedOn(List<Long> ids) {
        List<TimeoffDBO> timeoffDBOS = ids == null ?
                timeoffRepository.findAllWhereApprovedOnIsEmpty() :
                timeoffRepository.findAllById(ids);

        log.info("Found {} entities to backfill", timeoffDBOS.size());

        Map<Long, Instant> timeoffIdToapprovedOnMap = approvalServiceAdapter.getApprovedOnForItems(
                timeoffDBOS.stream().map(BaseEntity::id).toList()
        );

        timeoffDBOS.forEach(timeoff -> timeoff.approvedOn(
                timeoffIdToapprovedOnMap.getOrDefault(timeoff.id(), null)
        ));

        timeoffRepository.saveAll(timeoffDBOS);

        log.info("Saved entities successfully");
    }

    /**
     * <a href="https://app.clickup.com/t/865cqd1v6">Details</a>
     * @param contractIds the contract ids to check and fix
     * @return a list of debugging infos
     */
    @Transactional
    public List<String> fixEntitlementChangeRecords(Set<Long> contractIds) {
        if (CollectionUtils.isEmpty(contractIds)) {
            return List.of("There are 0 contract ids. Nothing to be done.");
        }

        Map<Long, List<EntitlementChangeRecordEntity>> contractIdToECRs = entitlementChangeRecordRepository.findAllByContractIdIn(contractIds)
                .stream()
                .collect(Collectors.groupingBy(EntitlementChangeRecordEntity::getContractId));

        // we can ignore the contracts that don't have any ECR records
        // for each contract in contractIdToECRs there should be its summaries, else corrupted data (or "test" data)
        Map<Long, List<TimeoffSummaryDBO>> contractIdToSummaries = timeoffSummaryRepository.findAllByContractIdIn(contractIdToECRs.keySet())
                .stream()
                .collect(Collectors.groupingBy(TimeoffSummaryDBO::contractId));

        List<String> infos = new ArrayList<>();
        contractIdToECRs.forEach((cId, entitlementChangeRecords) -> {
            val summaries = contractIdToSummaries.get(cId);
            if (CollectionUtils.isEmpty(summaries)) {
                infos.add(format("Contract {0} does not have any summary. Looks like a data corruption, so ignoring it.", cId));
                return;
            }

            for (val ecr : entitlementChangeRecords) {
                if (ecr.getCategory() != EntitlementChangeCategory.ALLOCATION) {
                    continue;
                }

                TimeoffSummaryDBO relatedSummary = findRelatedSummary(ecr, summaries);
                if (relatedSummary == null) {
                    infos.add(format("Contract {0} does not have any summary related to ECR: id={1}, typeId={2}, validFrom={3}, validToInclusive={4}. Ignoring.",
                            cId, ecr.getId(), ecr.getTypeId(), ecr.getValidFrom(), ecr.getValidToInclusive()));
                    continue;
                }

                if (Objects.equals(ecr.getCount(), relatedSummary.allocatedCount())) {
                    infos.add(format("The ECR of contract {0} is already correct; ECR.id={1}. Ignoring.", cId, ecr.getId()));
                    continue;
                }

                infos.add(format("The ECR.count of contract {0} will be set = summary.allocatedCount; ECR: id={1}, old count={2}, new count={3}.",
                        cId, ecr.getId(), ecr.getCount(), relatedSummary.allocatedCount()));
                ecr.setCount(relatedSummary.allocatedCount());
            }
        });

        return infos;
    }

    @Nullable
    private static TimeoffSummaryDBO findRelatedSummary(EntitlementChangeRecordEntity ecr, List<TimeoffSummaryDBO> summaries) {
        return summaries.stream()
                .filter(summary -> Objects.equals(summary.typeId(), ecr.getTypeId()))
                .filter(summary -> Objects.equals(summary.periodStart(), ecr.getValidFrom()))
                .filter(summary -> Objects.equals(summary.periodEnd(), ecr.getValidToInclusive()))
                .findFirst() // ideally only 1 match
                .orElse(null);
    }

    /**
     * <a href="https://app.clickup.com/t/865cq5cga">Details</a>
     *
     * @return debugging infos
     */
    @Transactional
    public List<String> changeTimeOffDate(long id, @NotNull LocalDate newStartDate, @NotNull LocalDate newEndDate, boolean ignoresValidations) {
        // mandatory validation, can't ignore
        if (newStartDate.isAfter(newEndDate)) {
            throw new ValidationException("Cannot change timeoff date because new start date is after new end date (bad input)");
        }

        val timeoffDBO = timeoffRepository.findById(id).orElseThrow();
        List<String> infos = new ArrayList<>();

        if (ignoresValidations) {
            infos.add("No validations are being applied. Make sure you have double-checked the timeoff summary cycle");
        } else {
            validateTimeoffStatus(timeoffDBO);
            validateYearMonthRange(timeoffDBO, newStartDate, newEndDate);
            infos.add("Validations passed. This request is safe to proceed.");
        }

        timeoffDBO
                .startDate(newStartDate)
                .endDate(newEndDate);

        timeoffKafkaPublisher.publishTimeoffUpdateEvent(mapper.map(timeoffDBO));

        return infos;
    }

    @Transactional
    public List<String> changeTimeOffDateV2(TimeOffChangeDateInput input) {
        // Extract fields from input
        long id = input.getId();
        LocalDate newStartDate = input.getStartDate();
        LocalDate newEndDate = input.getEndDate();
        boolean ignoresValidations = Boolean.TRUE.equals(input.getIgnoresValidations());

        // Mandatory validation can't be ignored
        if (newStartDate.isAfter(newEndDate)) {
            throw new ValidationException("Cannot change timeoff date because new start date is after new end date (bad input)");
        }

        val timeoffDBO = timeoffRepository.findById(id)
                .orElseThrow(() -> new ValidationException("Timeoff record not found with id: " + id));

        List<String> infos = new ArrayList<>();

        if (ignoresValidations) {
            infos.add("No validations are being applied. Make sure you have double-checked the timeoff summary cycle");
        } else {
            validateTimeoffStatus(timeoffDBO);
            validateYearMonthRange(timeoffDBO, newStartDate, newEndDate);
            infos.add("Validations passed. This request is safe to proceed.");
        }

        // Update the timeoff entity
        timeoffDBO
                .startDate(newStartDate)
                .endDate(newEndDate);

        // Calculate duration between startDate and endDate
        long durationBetween = ChronoUnit.DAYS.between(newStartDate, newEndDate) + 1;

        validateAndSetNoOfDays(input, durationBetween, timeoffDBO, infos);

        timeoffRepository.save(timeoffDBO);
        timeoffKafkaPublisher.publishTimeoffUpdateEvent(mapper.map(timeoffDBO));

        return infos;
    }

    private void validateAndSetNoOfDays(TimeOffChangeDateInput input, long durationBetween, TimeoffDBO timeoffDBO, List<String> infos) {
        if (input.getNoOfDays() != null) {
            double noOfDays = input.getNoOfDays();

            if (noOfDays > durationBetween) {
                throw new ValidationException("noOfDays cannot be greater than the duration between start and end date.");
            }

            // Valid case where noOfDays < durationBetween (e.g., due to holidays) is allowed
            infos.add("noOfDays is valid and within the permissible range.");
            timeoffDBO.noOfDays(noOfDays);
        }
    }

    private void validateTimeoffStatus(TimeoffDBO timeoffDBO) {
        if (!STATUSES_ALLOWED_TO_CHANGE_DATE.contains(timeoffDBO.status())) {
            throw new ValidationException("Cannot change timeoff date because current timeoff status is invalid: " + timeoffDBO.status());
        }
    }

    private void validateYearMonthRange(TimeoffDBO timeoffDBO, LocalDate newStartDate, LocalDate newEndDate) {
        val validYearMonths = SetUtils.hashSet(
                YearMonth.from(timeoffDBO.startDate()),
                YearMonth.from(timeoffDBO.endDate())
        );

        if (!validYearMonths.contains(YearMonth.from(newStartDate)) || !validYearMonths.contains(YearMonth.from(newEndDate))) {
            throw new ValidationException("Cannot change timeoff date because the new date range is invalid; Valid year-months: " + validYearMonths);
        }
    }

    private void throwIfUnauthorized(ContractOuterClass.Contract contract) {
        if (isSystemCall()) {
            // we allow calls originated from system schedulers to pass
            return;
        }
        if (isOpsExperience()) {
            return;
        }
        if (!isMyCompany(contract.getCompanyId())) {
            throw new AccessDeniedException(format("Access denied for user id : {0} for the contract id : {1}",
                    currentUser.getContext().getId(), contract.getId()));
        }
    }

    private void validateContractRequirements(@Nullable ContractOuterClass.Contract contract, DgsDataFetchingEnvironment dfe) {
        if (contract == null) {
            throw new ValidationException("Cannot find contract for the given id ");
        }
        validateAccess(contract, dfe);

        if (!CONTRACT_TYPES_ELIGIBLE_FOR_TIMEOFFS.contains(contract.getType())) {
            throw new ValidationException(String.format("Contract type is not eligible for timeoffs entitlements (id = %d, type = %s)", contract.getId(), contract.getType()));
        }
    }

    private void validateAccess(ContractOuterClass.Contract contract, DgsDataFetchingEnvironment dfe) {
        val experience = currentUser.getContext().getExperience();
        if (OPERATIONS_EXP.equals(experience)) {
            return;
        }
        authorizationService.authorize(dfe, contract);
    }

    private boolean isMyContract(Long memberId) {
        return currentUser.getContext().getScopes().getMemberId().equals(memberId);
    }

    /**
     * @return true when it looks like being called from the system/cronjob etc...
     */
    private boolean isSystemCall() {
        val ctx = currentUser.getContext();
        return ctx == null
                || ctx.getId() == null
                || ctx.getId() <= 0
                || StringUtils.isEmpty(ctx.getExperience());
    }

    private boolean isOpsExperience() {
        return currentUser.getContext().getExperience().equals(OPERATIONS_EXP);
    }

    private boolean isMyCompany(Long companyId) {
        return currentUser.getContext().getScopes().getCompanyId().equals(companyId);
    }

    @Transactional
    public TaskResponse bulkRevokeTimeOffs(final List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new TaskResponse(true, "Ids is empty. Nothing processed.");
        }
        var timeOffDBOs = timeoffRepository.findAllById(ids);
        validateTimeOffsForBulkRevoke(ids, timeOffDBOs);
        timeOffDBOs.forEach(timeOffDBO -> timeOffDBO.status(TimeOffStatus.DRAFT));
        timeOffDBOs = timeoffRepository.saveAllAndFlush(timeOffDBOs);
        timeOffDBOs.forEach(timeoffDBO -> {
            timeoffSummaryService.onTimeOffChange(timeoffDBO);
            timeoffKafkaPublisher.publishTimeoffUpdateEvent(mapper.map(timeoffDBO));
        });
        return new TaskResponse(true, String.format("Successfully Revoked %d Time Offs", ids.size()));
    }

    private void validateTimeOffsForBulkRevoke(List<Long> ids, List<TimeoffDBO> timeoffDBOs) {
        if (timeoffDBOs.size() < ids.size()) {
            Set<Long> foundIds = timeoffDBOs.stream().map(TimeoffDBO::id).collect(Collectors.toSet());
            List<Long> notFoundIds = ids.stream().filter(id -> !foundIds.contains(id)).toList();
            throw new ValidationException("Unable to find some of the time offs by id: " + notFoundIds);
        }
        var invalidTimeOffs = timeoffDBOs.stream()
                .filter(timeoffDBO -> !STATUSES_ALLOWED_TO_REVOKE_BY_OPS.contains(timeoffDBO.status()))
                .toList();
        if (!invalidTimeOffs.isEmpty()) {
            List<String> invalidTimeOffMessages = invalidTimeOffs.stream()
                    .map(timeOff -> String.format("{id: %s, status: %s}", timeOff.id(), timeOff.status()))
                    .toList();
            throw new ValidationException("Some of the given time offs cannot be processed due to their current status: " + invalidTimeOffMessages);
        }
    }

    /**
     * throw an exception if we cannot delete, or the values violate min/max
     *
     * @param inputs                 the inputs from FE
     * @param missingEntitlementDBOs the entitlements which have been in DB but not found in inputs
     */
    private void throwIfEORCannotUpdateEntitlements(ContractOuterClass.Contract contract,
                                                    List<ContractUpdateTimeOffEntitlementsInput> inputs,
                                                    Set<TimeoffEntitlementDBO> missingEntitlementDBOs,
                                                    Map<String, TimeoffTypeDBO> keyToTimeOffTypeMap,
                                                    Map<Long, TimeOffDefinitionEntity> typeIdToDefinitionMap) {

        throwIfCannotDeleteEntitlements(missingEntitlementDBOs, typeIdToDefinitionMap, contract);
        throwIfEntitlementsAreOutOfLimits(inputs, contract, keyToTimeOffTypeMap, typeIdToDefinitionMap);
    }

    private void throwIfCannotDeleteEntitlements(Set<TimeoffEntitlementDBO> entitlementsToDelete,
                                                 Map<Long, TimeOffDefinitionEntity> typeIdToDefinitionMap,
                                                 @NotNull ContractOuterClass.Contract contract) {
        if (CollectionUtils.isEmpty(entitlementsToDelete)) {
            return;
        }

        if (contract.getStatus() != ContractOuterClass.ContractStatus.ONBOARDING) {
            throw new ValidationException(
                    String.format("Deleting entitlements %s not permitted for contract (id : %d , status :%s)"
                            , getTypeIds(entitlementsToDelete), contract.getId(), contract.getStatus()));
        }

        Map<Long, CountryDefinitionEntity> typeIdToCountryDefinitionMap = definitionService.getTypeIdToEligibleCountryDefinitionMap(contract);

        Set<Long> requiredEntitlementsToDelete = entitlementsToDelete.stream()
                .filter(ent -> {
                    val timeOffDefinitionEntity = typeIdToDefinitionMap.get(ent.typeId());
                    val definition = timeOffDefinitionEntity != null ? timeOffDefinitionEntity.getDefinition() : null;
                    return isMandatoryEntitlement(typeIdToCountryDefinitionMap.get(ent.typeId()), definition);
                })
                .map(TimeoffEntitlementDBO::typeId)
                .collect(Collectors.toSet());

        if (!CollectionUtils.isEmpty(requiredEntitlementsToDelete)) {
            String message = String.format("Cannot delete 'Required' timeoff entitlements. Missing entitlements %s from input are " +
                    "required entitlements for contract id : %d", requiredEntitlementsToDelete, contract.getId());
            throw new ValidationException(message);
        }
    }

    private void throwIfEntitlementsAreOutOfLimits(List<ContractUpdateTimeOffEntitlementsInput> inputs,
                                                   ContractOuterClass.Contract contract,
                                                   Map<String, TimeoffTypeDBO> keyToTimeoffTypeMap,
                                                   Map<Long, TimeOffDefinitionEntity> typeIdToDefinitionMap) {
        // validate min/max
        for (ContractUpdateTimeOffEntitlementsInput input : inputs) {
            val type = keyToTimeoffTypeMap.get(input.getKey());
            if (type == null) {
                throw new ValidationException("No leave type found to update entitlement for :" + input.getKey());
            }
            val definitionForType = typeIdToDefinitionMap.get(type.id());
            if (definitionForType == null) {
                throw new DataCorruptionException(String.format("No definition found for (type id : %d , company id : %d)",
                        type.id(), contract.getCompanyId()));
            }
            val validation = getValidation(definitionForType, input.getUnit());
            if (validation == null) {
                log.warn("[throwIfCannotUpdateEntitlements] Cannot find the validation config for [country, type, unit]= {}", List.of(contract.getCountry(), input.getKey(), input.getUnit()));
                return;
            }
            throwIfInputIsOutOfLimit(input, contract.getId(), validation);
        }
    }

    private void throwIfInputIsOutOfLimit(ContractUpdateTimeOffEntitlementsInput input, Long contractId,
                                          TimeoffDefinitionValidationEntity validation) {
        val maxLimit = validation.getMaximumValue();
        val minLimit = validation.getMinimumValue();
        val entitledValue = input.getValue();
        if ((maxLimit != null && entitledValue > maxLimit) ||
                (minLimit != null && entitledValue <minLimit )){
            Map<String, Object> params = Map.of(
                    "min", String.valueOf(minLimit),
                    "max", String.valueOf(maxLimit),
                    "contractId", contractId,
                    "input", input
            );
            throw new ValidationException(new MPLError(MPLErrorType.MPL0013_ENTITLEMENT_VALUE_OUT_OF_MIN_MAX_RANGE, params));
        }
    }

    private @Nullable TimeoffDefinitionValidationEntity getValidation(TimeOffDefinitionEntity timeOffDefinition, TimeOffUnit unit) {
        if (timeOffDefinition == null || timeOffDefinition.getDefinition() == null) {
            return null;
        }
        return timeOffDefinition.getDefinition().getValidations().stream()
                .filter(v -> v.getUnit() == unit)
                .findFirst()
                .orElse(null);
    }


    private List<Long> getTypeIds(Set<TimeoffEntitlementDBO> entitlementDBOS) {
        return entitlementDBOS.stream()
                .map(TimeoffEntitlementDBO::typeId)
                .sorted()
                .toList();
    }

    private Map<String, TimeoffEntitlementDBO> getKeyToEntitlementMap(Map<Long, TimeoffTypeDBO> idToTimeOffTypeMap,
                                                                      List<TimeoffEntitlementDBO> entitlementDBOS) {
        return entitlementDBOS.stream()
                .collect(Collectors.toMap(
                        ent -> idToTimeOffTypeMap.get(ent.typeId()).key(),
                        Function.identity()));
    }

    private Set<TimeoffEntitlementDBO> findMissingEntitlementsInInput(Set<String> inputKeys, Map<String, TimeoffEntitlementDBO> keyToExistingEntitlementMap) {
        return keyToExistingEntitlementMap.entrySet().stream()
                .filter(entry -> !inputKeys.contains(entry.getKey()))
                .map(Map.Entry::getValue)
                .collect(Collectors.toSet());

    }


    public TimeOffContractRequirements getTimeOffContractRequirements(Long contractId, DgsDataFetchingEnvironment dfe) {
        val contract = contractServiceAdapter.getContractByIdAnyStatus(contractId);
        validateContractRequirements(contract, dfe);
        if (contract.getType() == ContractOuterClass.ContractType.HR_MEMBER) {
            return getHRMemberTimeOffRequirements(contract);
        }
        return getTimeOffContractRequirementsForEORContract(contract);
    }

    private TimeOffContractRequirements getHRMemberTimeOffRequirements(ContractOuterClass.Contract contract) {
        List<TimeoffEntitlementDBO> existingEntitlementDBOs = timeoffEntitlementDBORepository.findAllByContractId(contract.getId());
        Map<Long, TimeOffDefinitionEntity> typeIdToAvailableDefinitionMap = getTypeIdToDefinitionMapForHRMember(contract, existingEntitlementDBOs);
        val timeOffTypes = getIdToTimeOffTypeDBOMap(typeIdToAvailableDefinitionMap.keySet());

        val availableEntitlements = typeIdToAvailableDefinitionMap.values().stream()
                .map(definition -> mapAvailableEntitlement(definition, timeOffTypes))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        val assignedEntitlements = existingEntitlementDBOs.stream()
                .map(ent -> mapAssignedEntitlement(ent, typeIdToAvailableDefinitionMap.get(ent.typeId()), timeOffTypes))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return TimeOffContractRequirements.newBuilder()
                .clause("")
                .availableEntitlements(availableEntitlements)
                .assignedEntitlements(assignedEntitlements)
                .build();
    }

    private Map<Long, TimeOffDefinitionEntity> getTypeIdToDefinitionMapForHRMember(ContractOuterClass.Contract contract, Collection<TimeoffEntitlementDBO> existingEntitlementDBOs) {
        Map<Long, TimeOffDefinitionEntity> typeIdToAvailableDefinitionMap = definitionService.findAllDefinitionsForContract(contract)
                .stream()
                .collect(Collectors.toMap(TimeOffDefinitionEntity::getTypeId, Function.identity()));
        val defaultUnpaidDefinition = HrMemberTimeOffDefinitionKt.getUnpaidLeaveDefinition();
        if (!typeIdToAvailableDefinitionMap.containsKey(defaultUnpaidDefinition.getTypeId())) {
            // if no definition for unpaid leave -> we will add our default definition
            typeIdToAvailableDefinitionMap.put(defaultUnpaidDefinition.getTypeId(), defaultUnpaidDefinition);
        }
        if (isEntitlementPresentFromDefaultAnnualLeave(existingEntitlementDBOs, typeIdToAvailableDefinitionMap)) {
            // if entitlement from default annual leave present -> we will add our default definition
            val defaultAnnualDefinition = HrMemberTimeOffDefinitionKt.getAnnualLeaveDefinition();
            typeIdToAvailableDefinitionMap.put(defaultAnnualDefinition.getTypeId(), defaultAnnualDefinition);
        }
        return typeIdToAvailableDefinitionMap;
    }

    private boolean isEntitlementPresentFromDefaultAnnualLeave(Collection<TimeoffEntitlementDBO> existingEntitlementDBOs,
                                                               Map<Long, TimeOffDefinitionEntity> typeIdToAvailableDefinitionMap) {
        // if an HR member has given annual leave entitlement from a legacy system,
        // and company do not have any definition for annual leave, then we will add our default definition
        val isDefaultAnnualLeaveEntitlementPresent = existingEntitlementDBOs.stream()
                .anyMatch(ent -> ent.typeId().equals(HrMemberTimeOffDefinitionKt.getAnnualLeaveDefinition().getTypeId())
                        && ent.definitionId() == null);
        val isCompanyAnnualLeaveDefinitionPresent = typeIdToAvailableDefinitionMap.containsKey(HrMemberTimeOffDefinitionKt.getAnnualLeaveDefinition().getTypeId());
        return isDefaultAnnualLeaveEntitlementPresent && !isCompanyAnnualLeaveDefinitionPresent;
    }

    private ContractTimeOffEntitlement mapAvailableEntitlement(TimeOffDefinitionEntity definitionEntity,
                                                               Map<Long, TimeoffTypeDBO> idToTimeOffTypeDBOMap) {
        val entitledValue = getDefaultValue(definitionEntity.getDefinition());
        val type = idToTimeOffTypeDBOMap.get(definitionEntity.getTypeId());
        return mapToHrMemberContractEntitlement(definitionEntity, type, entitledValue);
    }

    private ContractTimeOffEntitlement mapAssignedEntitlement(TimeoffEntitlementDBO entitlementDBO,
                                                              TimeOffDefinitionEntity definitionEntity,
                                                              Map<Long, TimeoffTypeDBO> idToTimeOffTypeDBOMap) {
        val entitledValue =TimeOffDuration.newBuilder()
                .value(entitlementDBO.value())
                .unit(entitlementDBO.unit())
                .build();
        val type = idToTimeOffTypeDBOMap.get(entitlementDBO.typeId());
        return mapToHrMemberContractEntitlement(definitionEntity, type, entitledValue);

    }

    private TimeOffContractRequirements getTimeOffContractRequirementsForEORContract(ContractOuterClass.Contract contract) {
        List<TimeoffEntitlementDBO> existingEntitlements = timeoffEntitlementDBORepository.findAllByContractId(contract.getId());
        Map<Long, TimeOffDefinitionEntity> defIdToEligibleDefinitionMap = getDefinitionIdToEligibleDefinitionsMap(contract);
        Map<Long, TimeOffDefinitionEntity> defIdToExistingDefinitionMap = getDefinitionIdToExistingDefinitionsMap(contract.getCompanyId(), existingEntitlements, defIdToEligibleDefinitionMap);
        Map<Long, TimeOffDefinitionEntity> defIdToAvailableDefinitionMap = getDeinitionIdToAvailableDefinitionMap(defIdToEligibleDefinitionMap, defIdToExistingDefinitionMap);
        Map<Long, TimeoffTypeDBO> idToTimeOffTypeMap = getIdToTimeOffTypeDBOMap(getAllTypeIds(defIdToExistingDefinitionMap.values(), defIdToAvailableDefinitionMap.values()));
        Map<Long, CountryDefinitionEntity> typeIdToCountryDefinitionMap = definitionService.getTypeIdToEligibleCountryDefinitionMap(contract);

        val availableEntitlements = getAvailableEntitlementsForEORContract(defIdToAvailableDefinitionMap.values(), idToTimeOffTypeMap, typeIdToCountryDefinitionMap);
        val assignedEntitlements = getAssignedEntitlementsForEORContract(existingEntitlements, defIdToExistingDefinitionMap, idToTimeOffTypeMap, typeIdToCountryDefinitionMap);
        val clause = getClauseForContractCountry(defIdToAvailableDefinitionMap, typeIdToCountryDefinitionMap, contract);

        return TimeOffContractRequirements.newBuilder()
                .assignedEntitlements(assignedEntitlements)
                .availableEntitlements(availableEntitlements)
                .clause(clause)
                .build();
    }

    private Map<Long, TimeOffDefinitionEntity> getDeinitionIdToAvailableDefinitionMap(Map<Long, TimeOffDefinitionEntity> defIdToEligibleDefinitionMap,
                                                                                      Map<Long, TimeOffDefinitionEntity> defIdToExistingDefinitionMap) {
        Map<Long, TimeOffDefinitionEntity> typeIdToExistingDefinitionMap = defIdToExistingDefinitionMap.values().stream()
                .collect(Collectors.toMap(TimeOffDefinitionEntity::getTypeId, Function.identity()));
        Map<Long, TimeOffDefinitionEntity> defIdToAvailableDefinitionMap = new HashMap<>();
        defIdToEligibleDefinitionMap.forEach((defId, def) -> {
            if (defIdToExistingDefinitionMap.containsKey(defId)) {
                defIdToAvailableDefinitionMap.put(defId, def);
            } else {
                val eligibleDefinitionTypeId = def.getTypeId();
                if (typeIdToExistingDefinitionMap.containsKey(eligibleDefinitionTypeId)) {
                    // there is an existing entitlement from this type, but comes from a different definition outside
                    // the current eligible set
                    val existingDefinition = typeIdToExistingDefinitionMap.get(eligibleDefinitionTypeId);
                    defIdToAvailableDefinitionMap.put(existingDefinition.getDefinition().getId(), existingDefinition);
                } else {
                    // this is simply an available definition not yet assigned
                    defIdToAvailableDefinitionMap.put(defId, def);
                }
            }
        });

        // Add missing definitions from existing definitions
        defIdToExistingDefinitionMap.forEach((defId, def) -> {
            if (!defIdToAvailableDefinitionMap.containsKey(defId)) {
                defIdToAvailableDefinitionMap.put(defId, def);
            }
        });
        return defIdToAvailableDefinitionMap;
    }

    private Set<Long> getAllTypeIds(Collection<TimeOffDefinitionEntity> availableDefinitions, Collection<TimeOffDefinitionEntity> existingDefinitions) {
        List<TimeOffDefinitionEntity> allDefinitions = Stream.concat(availableDefinitions.stream(), existingDefinitions.stream())
                .toList();
        return getTypeIdsFromDefinitions(allDefinitions);
    }

    private Map<Long, TimeOffDefinitionEntity> getDefinitionIdToExistingDefinitionsMap(Long companyId,
                                                                                       Collection<TimeoffEntitlementDBO> existingEntitlements,
                                                                                       Map<Long, TimeOffDefinitionEntity> definitionIdToAvailableDefinitionsMap) {
        val missingDefinitionIds = existingEntitlements.stream()
                .map(TimeoffEntitlementDBO::definitionId)
                .filter(definitionId -> !definitionIdToAvailableDefinitionsMap.containsKey(definitionId))
                .collect(Collectors.toSet());
        Map<Long, TimeOffDefinitionEntity> definitionIdToMissingDefinitionsMap = missingDefinitionIds.isEmpty()
                ? Collections.emptyMap()
                : definitionService.findAllDefinitionsByIds(companyId, missingDefinitionIds);


        Map<Long, TimeOffDefinitionEntity> existingDefinitionsMap = new HashMap<>();
        existingEntitlements.forEach(entitlement -> {
            if (definitionIdToAvailableDefinitionsMap.containsKey(entitlement.definitionId())) {
                existingDefinitionsMap.put(entitlement.definitionId(), definitionIdToAvailableDefinitionsMap.get(entitlement.definitionId()));
            } else {
                existingDefinitionsMap.put(entitlement.definitionId(), definitionIdToMissingDefinitionsMap.get(entitlement.definitionId()));
            }
        });

        return existingDefinitionsMap;
    }


    private Map<Long, TimeOffDefinitionEntity> getDefinitionIdToEligibleDefinitionsMap(ContractOuterClass.Contract contract) {
        return definitionService.findAllDefinitionsForContract(contract).stream()
                .filter(t -> t.getDefinition() != null)
                .collect(Collectors.toMap(t -> t.getDefinition().getId(), Function.identity()));
    }

    private Set<Long> getTypeIdsFromDefinitions(Collection<? extends TimeOffDefinitionEntity> definitionEntities) {
        return definitionEntities.stream()
                .map(TimeOffDefinitionEntity::getTypeId)
                .collect(Collectors.toSet());
    }

    private List<ContractTimeOffEntitlement> getAvailableEntitlementsForEORContract(Collection<TimeOffDefinitionEntity> availableDefinitions,
                                                                                    Map<Long, TimeoffTypeDBO> idToTimeOffTypeDBOMap,
                                                                                    Map<Long, CountryDefinitionEntity> typeIdToCountryDefinitionMap) {
        return availableDefinitions
                .stream()
                .filter(Objects::nonNull)
                .map(definition -> {
                    val entitledValue = getDefaultValue(definition.getDefinition());
                    return mapToEORContractEntitlement(entitledValue,
                            definition,
                            idToTimeOffTypeDBOMap.get(definition.getTypeId()),
                            typeIdToCountryDefinitionMap.get(definition.getTypeId()));
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private List<ContractTimeOffEntitlement> getAssignedEntitlementsForEORContract(List<TimeoffEntitlementDBO> existingEntitlements,
                                                                                   Map<Long, TimeOffDefinitionEntity> definitionEntityMap,
                                                                                   Map<Long, TimeoffTypeDBO> idToTimeOffTypeMap,
                                                                                   Map<Long, CountryDefinitionEntity> typeIdToCountryDefinitionMap) {
        return existingEntitlements.stream()
                .filter(ent -> definitionEntityMap.containsKey(ent.definitionId()))
                .map(ent -> {
                    val entitledValue = TimeOffDuration.newBuilder()
                            .value(ent.value())
                            .unit(ent.unit())
                            .build();
                    return mapToEORContractEntitlement(
                            entitledValue,
                            definitionEntityMap.get(ent.definitionId()),
                            idToTimeOffTypeMap.get(ent.typeId()),
                            typeIdToCountryDefinitionMap.get(ent.typeId()));
                })
                .collect(Collectors.toList());
    }

    private ContractTimeOffEntitlement mapToHrMemberContractEntitlement(TimeOffDefinitionEntity definitionEntity,
                                                                        TimeoffTypeDBO timeOffType,
                                                                        TimeOffDuration entitledValue) {
        if (timeOffType == null) {
            return null;
        }
        return ContractTimeOffEntitlement.newBuilder()
                .value(entitledValue.getValue())
                .unit(entitledValue.getUnit())
                .timeOffType(timeoffTypeMapper.mapToGraph(timeOffType))
                .definition(timeOffTypeDefinitionMapper.map(definitionEntity.getDefinition(), timeOffType))
                .isMandatory(false) // HR Member all definitions are company definitions -> hence all are non-mandatory
                .build();
    }

    private ContractTimeOffEntitlement mapToEORContractEntitlement(TimeOffDuration timeOffDuration,
                                                                   TimeOffDefinitionEntity definitionEntity,
                                                                   TimeoffTypeDBO timeOffType,
                                                                   CountryDefinitionEntity countryDefinitionEntity) {
        if (timeOffType == null) {
            return null;
        }
        boolean isMandatory = isMandatoryEntitlement(countryDefinitionEntity, definitionEntity.getDefinition());
        return ContractTimeOffEntitlement.newBuilder()
                .definition(timeOffTypeDefinitionMapper.map(definitionEntity.getDefinition(), timeOffType))
                .value(timeOffDuration.getValue())
                .unit(timeOffDuration.getUnit())
                .timeOffType(timeoffTypeMapper.mapToGraph(timeOffType))
                .isMandatory(isMandatory)
                .build();
    }

    private String getClauseForContractCountry(Map<Long, TimeOffDefinitionEntity> definitionIdToTimeOffDefinitionEntityMap,
                                               Map<Long, CountryDefinitionEntity> typeIdToCountryDefinitionMap,
                                               ContractOuterClass.Contract contract) {
        if (contract.getType() == ContractOuterClass.ContractType.HR_MEMBER) {
            return "";
        }
        var clause = definitionIdToTimeOffDefinitionEntityMap.values().stream()
                .filter(timeOffDefinitionEntity -> timeOffDefinitionEntity.getDefinition() != null)
                .filter(timeOffDefinitionEntity -> StringUtils.isNotEmpty(timeOffDefinitionEntity.getDefinition().getClause()))
                .findFirst()
                .map(timeOffDefinitionEntity -> timeOffDefinitionEntity.getDefinition().getClause())
                .orElse("");

        if (StringUtils.isEmpty(clause)) {
            clause = typeIdToCountryDefinitionMap.values().stream()
                    .filter(countryDefinitionEntity -> StringUtils.isNotEmpty(countryDefinitionEntity.getDefinition().getClause()))
                    .findFirst()
                    .map(countryDefinitionEntity -> countryDefinitionEntity.getDefinition().getClause())
                    .orElse("");
        }
        return clause;
    }

    @Data
    @Builder
    private static class BackFillEntitlementDefinitionIdResult {
        private Long successCount;
        private Long foundCount;
        private List<Long> failedEntitlementIds;
        private List<Long> contractNotFoundIds;
        private Set<Long> processedIds;
    }

}
